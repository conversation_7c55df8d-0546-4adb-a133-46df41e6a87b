import os
import xarray as xr
import numpy as np
import cartopy.crs as ccrs
import matplotlib.pyplot as plt

# 设置文件路径
data_dir = os.path.normpath(r'D:\dataset\supercooled')
bc_results_file = os.path.join(data_dir, 'bc_results.nc')
sulaodd_file = os.path.join(data_dir, 'sulaodd.nc')

def load_results(file_path, var_prefix, tau):
    try:
        # 确保文件路径是字符串类型
        file_path = str(file_path) if not isinstance(file_path, str) else file_path
        # 使用engine='netcdf4'参数确保正确读取
        ds = xr.open_dataset(file_path, engine='netcdf4')
        return (ds[f'{var_prefix}_tau{tau}'].values,
                ds[f'{var_prefix}_tau{tau}_sig'].values,
                ds['lat'].values,
                ds['lon'].values)
    except Exception as e:
        print(f"读取变量错误: {e}")
        return None, None, None, None

def plot_spatial(data, sig_data, lats, lons, title, save_name, tau):
    if data is None:
        print(f"数据为空，跳过绘图: {title}")
        return
        
    plt.figure(figsize=(12, 8))
    ax = plt.axes(projection=ccrs.PlateCarree())
    ax.coastlines()
    
    im = ax.pcolormesh(lons, lats, data, cmap='RdBu_r',
                      transform=ccrs.PlateCarree(), vmin=-1, vmax=1)
    plt.colorbar(im, ax=ax, label='Causal Strength', orientation='horizontal', pad=0.05)
    
    gl = ax.gridlines(crs=ccrs.PlateCarree(), draw_labels=True,
                     linewidth=1, color='gray', alpha=0.5, linestyle='--',
                     xlocs=range(-180, 181, 60),
                     ylocs=[-90, -60, -30, 0, 30, 60, 90])
    gl.top_labels = False
    gl.right_labels = False
    
    # 修改标题显示规则
    if tau == 1:
        ax.set_title(f'{title} (Lag {tau} month)')  # 1个月不加s
    else:
        ax.set_title(f'{title} (Lag {tau} months)')  # 2个月和3个月加s
    
    # 添加显著性标记点
    lat_grid = np.arange(0, len(lats), 5)
    lon_grid = np.arange(0, len(lons), 5)
    
    for lat_idx in lat_grid:
        for lon_idx in lon_grid:
            if sig_data[lat_idx, lon_idx]:
                ax.scatter(lons[lon_idx], lats[lat_idx],
                         color='black', s=3, transform=ccrs.PlateCarree())
    
    # 修改保存图片的路径处理
    plt.savefig(os.path.join(data_dir, f'{save_name}_tau{tau}.png'), dpi=600, bbox_inches='tight')
    plt.close()

print("开始绘图...")

# 绘制BCAOD550到TCSLW的结果
for tau in [1, 2, 3]:
    data, sig_data, lats, lons = load_results(bc_results_file, 'bcaod550_to_tcslw', tau)
    if data is not None:
        plot_spatial(data, sig_data, lats, lons,
                   'Causal Effect of BCAOD550 on TCSLW',
                   'bcaod550_to_tcslw', tau)

# 绘制TCSLW到BCAOD550的结果
for tau in [1, 2, 3]:
    data, sig_data, lats, lons = load_results(bc_results_file, 'tcslw_to_bcaod550', tau)
    if data is not None:
        plot_spatial(data, sig_data, lats, lons,
                   'Causal Effect of TCSLW on BCAOD550',
                   'tcslw_to_bcaod550', tau)

print("绘图完成！")