import xarray as xr
import numpy as np
import matplotlib.pyplot as plt
import cartopy.crs as ccrs
import cartopy.feature as cfeature

def analyze_causal_patterns():
    """
    分析AOD对TCSLW因果效应的空间模式及其成因
    """
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
    plt.rcParams['axes.unicode_minus'] = False
    
    # 加载因果效应数据
    causal_ds = xr.open_dataset('D:/dataset/supercooled/aod_to_tcslw_multi_tau_causal_effect.nc')
    causal_effect = causal_ds['aod_to_tcslw_tau1']
    significance = causal_ds['aod_to_tcslw_sig_tau1']
    
    # 只分析显著区域
    significant_effect = causal_effect.where(significance == 1)
    
    # 1. 按纬度带分析
    print("=== 纬度带分析 ===")
    lat_bands = {
        '热带': (significant_effect.sel(lat=slice(-23.5, 23.5))),
        '北半球中纬度': (significant_effect.sel(lat=slice(23.5, 66.5))),
        '南半球中纬度': (significant_effect.sel(lat=slice(-66.5, -23.5))),
        '北极': (significant_effect.sel(lat=slice(66.5, 90))),
        '南极': (significant_effect.sel(lat=slice(-90, -66.5)))
    }
    
    for band_name, data in lat_bands.items():
        if data.count() > 0:
            positive_ratio = (data > 0).sum() / data.count() * 100
            negative_ratio = (data < 0).sum() / data.count() * 100
            mean_effect = data.mean().item()
            print(f"{band_name}: 正因果{positive_ratio:.1f}%, 负因果{negative_ratio:.1f}%, 平均效应{mean_effect:.4f}")
    
    # 2. 按海陆分布分析
    print("\n=== 海陆分布分析 ===")
    # 简单的海陆掩码（基于经验阈值）
    land_mask = create_simple_land_mask(causal_ds.lat, causal_ds.lon)
    
    ocean_effect = significant_effect.where(~land_mask)
    land_effect = significant_effect.where(land_mask)
    
    if ocean_effect.count() > 0:
        ocean_pos = (ocean_effect > 0).sum() / ocean_effect.count() * 100
        ocean_neg = (ocean_effect < 0).sum() / ocean_effect.count() * 100
        print(f"海洋: 正因果{ocean_pos:.1f}%, 负因果{ocean_neg:.1f}%, 平均{ocean_effect.mean().item():.4f}")
    
    if land_effect.count() > 0:
        land_pos = (land_effect > 0).sum() / land_effect.count() * 100
        land_neg = (land_effect < 0).sum() / land_effect.count() * 100
        print(f"陆地: 正因果{land_pos:.1f}%, 负因果{land_neg:.1f}%, 平均{land_effect.mean().item():.4f}")
    
    # 3. 按气候带分析
    print("\n=== 气候带分析 ===")
    climate_zones = {
        '赤道辐合带': significant_effect.sel(lat=slice(-10, 10)),
        '副热带高压带': significant_effect.sel(lat=slice(20, 40)).mean(dim='lat') + 
                        significant_effect.sel(lat=slice(-40, -20)).mean(dim='lat'),
        '西风带': significant_effect.sel(lat=slice(40, 60)).mean(dim='lat') + 
                 significant_effect.sel(lat=slice(-60, -40)).mean(dim='lat')
    }
    
    for zone_name, data in climate_zones.items():
        if hasattr(data, 'count') and data.count() > 0:
            pos_ratio = (data > 0).sum() / data.count() * 100
            neg_ratio = (data < 0).sum() / data.count() * 100
            print(f"{zone_name}: 正因果{pos_ratio:.1f}%, 负因果{neg_ratio:.1f}%")
    
    # 4. 绘制分析图
    fig = plt.figure(figsize=(15, 10))
    
    # 子图1: 原始因果效应
    ax1 = plt.subplot(2, 2, 1, projection=ccrs.PlateCarree())
    im1 = ax1.contourf(causal_ds.lon, causal_ds.lat, significant_effect, 
                       levels=np.linspace(-1, 1, 21), cmap='RdBu_r', 
                       transform=ccrs.PlateCarree())
    ax1.add_feature(cfeature.COASTLINE)
    ax1.set_title('AOD对TCSLW的因果效应')
    plt.colorbar(im1, ax=ax1, shrink=0.8)
    
    # 子图2: 正负因果区域
    ax2 = plt.subplot(2, 2, 2, projection=ccrs.PlateCarree())
    binary_effect = xr.where(significant_effect > 0, 1, 
                            xr.where(significant_effect < 0, -1, 0))
    im2 = ax2.contourf(causal_ds.lon, causal_ds.lat, binary_effect,
                       levels=[-1.5, -0.5, 0.5, 1.5], colors=['blue', 'white', 'red'],
                       transform=ccrs.PlateCarree())
    ax2.add_feature(cfeature.COASTLINE)
    ax2.set_title('正负因果区域分布')
    
    # 子图3: 纬度平均
    ax3 = plt.subplot(2, 2, 3)
    lat_mean = significant_effect.mean(dim='lon')
    ax3.plot(lat_mean.lat, lat_mean, 'b-', linewidth=2)
    ax3.axhline(y=0, color='k', linestyle='--', alpha=0.5)
    ax3.set_xlabel('纬度')
    ax3.set_ylabel('因果效应')
    ax3.set_title('纬度平均因果效应')
    ax3.grid(True, alpha=0.3)
    
    # 子图4: 经度平均
    ax4 = plt.subplot(2, 2, 4)
    lon_mean = significant_effect.mean(dim='lat')
    ax4.plot(lon_mean.lon, lon_mean, 'r-', linewidth=2)
    ax4.axhline(y=0, color='k', linestyle='--', alpha=0.5)
    ax4.set_xlabel('经度')
    ax4.set_ylabel('因果效应')
    ax4.set_title('经度平均因果效应')
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('causal_pattern_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()

def create_simple_land_mask(lat, lon):
    """
    创建简单的海陆掩码
    """
    # 这是一个简化的海陆掩码，实际应用中应该使用更精确的数据
    land_mask = xr.zeros_like(xr.DataArray(np.zeros((len(lat), len(lon))), 
                                          coords=[lat, lon], dims=['lat', 'lon']))
    
    # 简单标记一些主要陆地区域（这里只是示例）
    # 实际应该使用地形数据或海陆掩码数据
    return land_mask.astype(bool)

if __name__ == "__main__":
    analyze_causal_patterns()
