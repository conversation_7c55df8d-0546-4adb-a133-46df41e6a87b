import xarray as xr
import numpy as np
import cartopy.crs as ccrs
import matplotlib.pyplot as plt

# 设置文件路径
data_dir = r'D:\dataset\supercooled'

# 读取NC文件
ds = xr.open_dataset(f'{data_dir}/tcslwnodelay_aod-tcslwcausal_effect_with_sig.nc')
tcslw_to_aod = ds['tcslwnodelay'].values
tcslw_sig = ds['tcslwnodelay_sig'].values
lats = ds['lat'].values
lons = ds['lon'].values

# 调整经度范围，确保数据连续性
lons = np.where(lons > 180, lons - 360, lons)
data = np.where(tcslw_to_aod > 1, np.nan, tcslw_to_aod)  # 处理异常值

# 绘制空间分布图
def plot_spatial(data, sig_data, title, save_name):
    plt.figure(figsize=(12, 8))
    ax = plt.axes(projection=ccrs.PlateCarree())
    ax.coastlines()
    
    # 使用pcolormesh绘制颜色图
    im = ax.pcolormesh(lons, lats, data, cmap='RdBu_r', 
                      transform=ccrs.PlateCarree(), vmin=-1, vmax=1)
    
    # 修改颜色条设置
    cbar = plt.colorbar(im, ax=ax, orientation='horizontal', pad=0.05,
                        label='Causal Effect Strength', 
                        ticks=np.arange(-1, 1.1, 0.25),
                        extend='neither')  # 去掉图例箭头
    
    # 网格线设置
    gl = ax.gridlines(crs=ccrs.PlateCarree(), draw_labels=True,
                     linewidth=1, color='gray', alpha=0.5, linestyle='--',
                     xlocs=range(-180, 181, 60),  # 经度每60度显示一个刻度
                     ylocs=[-90, -60, -30, 0, 30, 60, 90])  # 纬度设置
    gl.top_labels = False
    gl.right_labels = False
    
    sig_lats_indices, sig_lons_indices = np.where(sig_data)
    
    lat_grid = np.arange(0, len(lats), 4)
    lon_grid = np.arange(0, len(lons), 4)
    
    for lat_idx in lat_grid:
        for lon_idx in lon_grid:
            if sig_data[lat_idx, lon_idx]:
                ax.scatter(lons[lon_idx], lats[lat_idx], 
                           color='black', s=3, transform=ccrs.PlateCarree())
    
    ax.set_title(title)
    plt.savefig(f'{data_dir}/{save_name}.png', dpi=600, bbox_inches='tight')
    plt.show()

# 绘制图
print("开始绘图...")
plot_spatial(tcslw_to_aod, tcslw_sig, 
            'Causal Effect of AOD550 on TCSLW', 'aodnodelay_tcslw_to_aod')
print("处理完成！")