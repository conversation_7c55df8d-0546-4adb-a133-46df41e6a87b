# 使用CDO合并MERRA-2数据文件

## 🎯 目标
将下载的541个MERRA-2 AOD NetCDF文件合并成一个文件：`80-24merra2.nc`

## 📋 前提条件
- Ubuntu系统 (或WSL)
- 已下载的MERRA-2 .nc4文件
- CDO (Climate Data Operators)

## 🚀 快速合并 (推荐)

### 方法1: 使用简化脚本
```bash
# 进入脚本目录
cd /mnt/d/dataset/program/

# 运行简化合并脚本
bash simple_cdo_merge.sh
```

### 方法2: 使用完整脚本
```bash
# 运行完整的CDO合并脚本
bash merge_with_cdo.sh
```

### 方法3: 手动CDO命令
```bash
# 进入数据目录
cd /mnt/d/dataset/supercooled/merra2/

# 直接使用CDO合并
cdo mergetime *.nc4 /mnt/d/dataset/supercooled/80-24merra2.nc
```

## 🔧 安装CDO (如果需要)

### Ubuntu/Debian:
```bash
sudo apt update
sudo apt install cdo
```

### 验证安装:
```bash
cdo --version
```

## 📊 CDO合并的优势

1. **高效**: 比Python xarray更快
2. **内存友好**: 不需要将所有数据加载到内存
3. **专业**: 专门为气候数据设计
4. **稳定**: 处理大文件更可靠

## 🔍 验证合并结果

### 检查文件信息:
```bash
# 文件大小
ls -lh /mnt/d/dataset/supercooled/80-24merra2.nc

# 数据集信息
cdo sinfon /mnt/d/dataset/supercooled/80-24merra2.nc

# 时间步数
cdo ntime /mnt/d/dataset/supercooled/80-24merra2.nc

# 变量列表
cdo showname /mnt/d/dataset/supercooled/80-24merra2.nc

# 时间范围
cdo showtimestamp /mnt/d/dataset/supercooled/80-24merra2.nc | head -1
cdo showtimestamp /mnt/d/dataset/supercooled/80-24merra2.nc | tail -1
```

### 使用Python验证:
```python
import xarray as xr

# 打开合并后的文件
ds = xr.open_dataset('/mnt/d/dataset/supercooled/80-24merra2.nc')

print("变量:", list(ds.variables.keys()))
print("维度:", dict(ds.dims))
print("时间范围:", ds.time.min().values, "到", ds.time.max().values)

ds.close()
```

## 🛠️ 故障排除

### 1. CDO未安装
```bash
# 安装CDO
sudo apt update && sudo apt install -y cdo
```

### 2. 内存不足
```bash
# 使用分批合并 (在完整脚本中自动处理)
# 或者增加交换空间
sudo fallocate -l 4G /swapfile
sudo chmod 600 /swapfile
sudo mkswap /swapfile
sudo swapon /swapfile
```

### 3. 文件损坏
```bash
# 检查单个文件
cdo sinfon filename.nc4

# 修复损坏的文件 (如果可能)
cdo copy input.nc4 output_fixed.nc4
```

### 4. 磁盘空间不足
```bash
# 检查可用空间
df -h /mnt/d/

# 清理临时文件
rm -rf /tmp/merra2_*
```

## 📁 文件路径说明

- **输入目录**: `/mnt/d/dataset/supercooled/merra2/`
- **输入文件**: `*.nc4` (541个文件)
- **输出文件**: `/mnt/d/dataset/supercooled/80-24merra2.nc`

## ⚡ 性能优化

### 1. 使用SSD存储
如果可能，将数据存储在SSD上以提高I/O性能。

### 2. 增加内存
```bash
# 检查当前内存使用
free -h

# 监控合并过程中的内存使用
watch -n 5 'free -h && df -h /mnt/d/'
```

### 3. 并行处理 (高级)
```bash
# 如果有多个CPU核心，可以使用并行CDO
export CDO_PTHREADS=4
cdo mergetime *.nc4 output.nc
```

## 🧹 清理原始文件

合并完成后，可以选择清理原始文件以节省空间：

### 1. 压缩原始文件
```bash
cd /mnt/d/dataset/supercooled/merra2/
tar -czf original_nc4_files.tar.gz *.nc4
```

### 2. 删除原始文件 (谨慎操作)
```bash
# 确认合并文件正确后再删除
rm *.nc4
```

### 3. 验证压缩文件
```bash
# 测试压缩文件完整性
tar -tzf original_nc4_files.tar.gz > /dev/null && echo "压缩文件完整"
```

## 📈 预期结果

- **输入**: 541个 .nc4 文件 (~10-25GB)
- **输出**: 1个 .nc 文件 (~8-20GB)
- **时间跨度**: 1980-2024年
- **变量**: AODANA (AOD at 550nm)
- **时间分辨率**: 月平均
- **空间分辨率**: 0.5° × 0.625°

## 🎉 完成检查清单

- [ ] CDO已安装
- [ ] 所有.nc4文件已下载
- [ ] 有足够的磁盘空间
- [ ] 合并命令执行成功
- [ ] 输出文件存在且大小合理
- [ ] 数据完整性验证通过
- [ ] 原始文件已备份/清理

## 💡 提示

1. **备份重要**: 合并前确保有原始文件的备份
2. **验证数据**: 合并后验证时间连续性和数据完整性
3. **监控进程**: 大文件合并可能需要较长时间
4. **磁盘空间**: 确保有足够空间存储合并后的文件

祝合并顺利！🎯
