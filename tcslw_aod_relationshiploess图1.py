import xarray as xr
import numpy as np
import matplotlib.pyplot as plt
from statsmodels.nonparametric.smoothers_lowess import lowess
from scipy.stats import gaussian_kde

# 读取数据
tcslw = xr.open_dataset('D:/dataset/supercooled/remonthly_avg_tcslw.nc')
aod_tcslw = xr.open_dataset('D:/dataset/supercooled/aod550_to_tcslw_multi_tau.nc')

# 处理数据并筛选
x = tcslw['tcslw'].values.flatten()
y = aod_tcslw['aod550_to_tcslw_tau1'].values.flatten()  # 使用新的变量名
mask = (~np.isnan(x)) & (~np.isnan(y)) & (x > 0) & (y != 0)
x = x[mask]
y = y[mask]

# 创建图形和双坐标轴
fig, ax1 = plt.subplots(figsize=(12, 8))

# 计算概率密度
xy = np.vstack([x, y])
kde = gaussian_kde(xy)
z = kde(xy)

# 绘制散点图（颜色表示概率密度）
sc = ax1.scatter(x, y, c=z, s=10, alpha=0.5, cmap='viridis', label='Data points')

# LOESS拟合
loess_smoothed = lowess(y, x, frac=0.3, it=3)
ax1.plot(loess_smoothed[:, 0], loess_smoothed[:, 1], 'r-', linewidth=3, label='LOESS fit')

# 设置x轴范围在0.01-0.12之间
ax1.set_xlim(0.01, 0.12)
ax1.set_xlabel('TCSLW (kg m$^{-2}$)')
ax1.set_ylabel('Causal Effect of AOD on TCSLW')

# 添加颜色条
cbar = plt.colorbar(sc, ax=ax1, label='Probability Density')

# 图形元素
ax1.set_title('The LOESS Relationship between TCSLW and the Causal Effect of AOD on TCSLW')
ax1.legend(loc='upper left')
ax1.grid(True)

# 保存图形
plt.savefig('D:/dataset/supercooled/tcslw_aod_loess_0.01-0.12.png', dpi=600, bbox_inches='tight')
plt.show()