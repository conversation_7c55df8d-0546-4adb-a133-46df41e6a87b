import os
import requests
import xarray as xr
from pathlib import Path
import time
from urllib.parse import urljoin, urlparse
import re

class EarthdataDownloader:
    def __init__(self, username, password):
        self.username = username
        self.password = password
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
    def authenticate(self):
        """进行NASA Earthdata认证"""
        try:
            # 首先访问一个需要认证的URL来触发登录流程
            auth_url = "https://urs.earthdata.nasa.gov/oauth/authorize"
            
            # 获取登录页面
            response = self.session.get(auth_url)
            
            # 查找登录表单的action URL
            login_url = "https://urs.earthdata.nasa.gov/login"
            
            # 准备登录数据
            login_data = {
                'username': self.username,
                'password': self.password,
                'client_id': 'e2WVk8Pw6weeLUKZYOxxvTQ',
                'redirect_uri': 'https://goldsmr4.gesdisc.eosdis.nasa.gov/data-redirect',
                'response_type': 'code'
            }
            
            # 执行登录
            response = self.session.post(login_url, data=login_data, allow_redirects=True)
            
            if response.status_code == 200:
                print("✓ NASA Earthdata认证成功")
                return True
            else:
                print(f"✗ 认证失败: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"认证过程出错: {e}")
            return False
    
    def download_file(self, url, file_path, max_retries=3):
        """下载单个文件"""
        for attempt in range(max_retries):
            try:
                print(f"  尝试下载 (第{attempt+1}次)...")
                
                # 直接下载，session已经包含认证信息
                response = self.session.get(url, stream=True, timeout=300, allow_redirects=True)
                
                # 检查是否被重定向到登录页面
                if 'urs.earthdata.nasa.gov' in response.url and 'login' in response.url:
                    print("  需要重新认证...")
                    if self.authenticate():
                        continue
                    else:
                        return False
                
                response.raise_for_status()
                
                # 写入文件
                with open(file_path, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:
                            f.write(chunk)
                
                # 验证文件大小
                if file_path.stat().st_size > 1000:  # 至少1KB
                    print(f"  ✓ 下载成功 ({file_path.stat().st_size} bytes)")
                    return True
                else:
                    print("  ✗ 文件太小，可能下载失败")
                    file_path.unlink()  # 删除无效文件
                    
            except Exception as e:
                print(f"  下载尝试 {attempt + 1} 失败: {e}")
                if file_path.exists():
                    file_path.unlink()  # 删除部分下载的文件
                
                if attempt < max_retries - 1:
                    time.sleep(5)
        
        return False

def main():
    """主函数"""
    print("MERRA-2 AOD数据下载工具 (改进版)")
    print("=" * 40)
    
    # 配置
    username = "willzhangyu1991"
    password = "Zy.768010991"
    
    url_file_path = r'D:\willz\download\subset_M2IMNXGAS_5.12.4_20250809_073842_.txt'
    download_dir = Path(r'D:\dataset\supercooled\merra2')
    output_file = r'D:\dataset\supercooled\80-24aodmerra2.nc'
    
    print(f"URL文件路径: {url_file_path}")
    print(f"下载目录: {download_dir}")
    print(f"输出文件: {output_file}")
    
    # 创建下载目录
    download_dir.mkdir(parents=True, exist_ok=True)
    
    # 检查URL文件
    if not os.path.exists(url_file_path):
        print(f"错误: 找不到文件 {url_file_path}")
        return
    
    # 读取URL
    print("\n正在读取下载地址...")
    try:
        with open(url_file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        urls = []
        for line in lines:
            line = line.strip()
            if line and (line.startswith('http://') or line.startswith('https://')):
                urls.append(line)
        
        print(f"找到 {len(urls)} 个下载地址")
        
        if len(urls) == 0:
            print("没有找到有效的下载地址")
            return
            
    except Exception as e:
        print(f"读取文件出错: {e}")
        return
    
    # 创建下载器并认证
    downloader = EarthdataDownloader(username, password)
    print(f"\n正在进行NASA Earthdata认证...")
    
    if not downloader.authenticate():
        print("认证失败，无法继续下载")
        return
    
    # 下载文件
    downloaded_files = []
    failed_downloads = []
    
    print(f"\n开始下载 {len(urls)} 个文件...")
    start_time = time.time()
    
    for i, url in enumerate(urls, 1):
        try:
            # 从URL提取文件名
            filename = os.path.basename(url.split('?')[0])
            if not filename.endswith('.nc4'):
                filename = f"merra2_aod_{i:04d}.nc4"
            
            file_path = download_dir / filename
            
            # 检查文件是否已存在
            if file_path.exists() and file_path.stat().st_size > 1000:
                print(f"[{i}/{len(urls)}] {filename} 已存在，跳过")
                downloaded_files.append(str(file_path))
                continue
            
            print(f"[{i}/{len(urls)}] 正在下载 {filename}...")
            
            # 下载文件
            success = downloader.download_file(url, file_path)
            
            if success:
                downloaded_files.append(str(file_path))
                print(f"[{i}/{len(urls)}] ✓ {filename} 下载完成")
            else:
                failed_downloads.append(url)
                print(f"[{i}/{len(urls)}] ✗ {filename} 下载失败")
            
            # 显示进度
            elapsed = time.time() - start_time
            if i % 10 == 0:
                avg_time = elapsed / i
                remaining = (len(urls) - i) * avg_time
                print(f"  进度: {i}/{len(urls)} ({i/len(urls)*100:.1f}%), 预计剩余时间: {remaining/3600:.1f}小时")
            
            # 添加延迟
            time.sleep(1)
            
        except Exception as e:
            failed_downloads.append(url)
            print(f"[{i}/{len(urls)}] ✗ 下载失败: {e}")
    
    print(f"\n下载完成:")
    print(f"✓ 成功: {len(downloaded_files)} 个文件")
    print(f"✗ 失败: {len(failed_downloads)} 个文件")
    print(f"总耗时: {(time.time() - start_time)/3600:.1f} 小时")
    
    if len(downloaded_files) == 0:
        print("没有成功下载任何文件")
        return
    
    # 合并文件
    print(f"\n开始合并 {len(downloaded_files)} 个文件...")
    try:
        datasets = []
        valid_files = []
        
        for file_path in downloaded_files:
            try:
                ds = xr.open_dataset(file_path)
                datasets.append(ds)
                valid_files.append(file_path)
                print(f"✓ 加载 {os.path.basename(file_path)}")
            except Exception as e:
                print(f"✗ 加载失败 {os.path.basename(file_path)}: {e}")
        
        if len(datasets) == 0:
            print("没有有效的数据集可以合并")
            return
        
        print("正在合并数据集...")
        merged_ds = xr.concat(datasets, dim='time')
        merged_ds = merged_ds.sortby('time')
        
        print(f"正在保存到 {output_file}...")
        merged_ds.to_netcdf(output_file)
        
        # 关闭数据集
        for ds in datasets:
            ds.close()
        merged_ds.close()
        
        print(f"\n✓ 处理完成！")
        print(f"✓ 成功下载: {len(downloaded_files)} 个文件")
        print(f"✓ 成功合并: {len(valid_files)} 个文件")
        print(f"✓ 输出文件: {output_file}")
        
        # 显示文件信息
        with xr.open_dataset(output_file) as ds:
            print(f"\n合并后的数据集信息:")
            print(f"变量: {list(ds.variables.keys())}")
            print(f"维度: {dict(ds.dims)}")
            if 'time' in ds.dims:
                print(f"时间范围: {ds.time.min().values} 到 {ds.time.max().values}")
        
        # 保存失败列表
        if failed_downloads:
            failed_file = download_dir / "failed_downloads.txt"
            with open(failed_file, 'w') as f:
                for url in failed_downloads:
                    f.write(url + '\n')
            print(f"\n失败的下载地址已保存到: {failed_file}")
        
    except Exception as e:
        print(f"合并过程出错: {e}")

if __name__ == "__main__":
    main()
