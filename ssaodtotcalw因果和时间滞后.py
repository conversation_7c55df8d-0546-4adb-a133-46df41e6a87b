# -*- coding: utf-8 -*-
import xarray as xr
import numpy as np
import pandas as pd
from tigramite import data_processing as pp
from tigramite.pcmci import PCMCI
from tigramite.independence_tests.parcorr import ParCorr
from joblib import Parallel, delayed
from tqdm import tqdm

# 设置文件路径和参数
data_dir = r'D:\dataset\supercooled'
aod_file = 'reBc_bcaod550.nc'
water_file = '03-23supercooledwater.nc'

def load_data(var_file, var_name):
    ds = xr.open_dataset(f'{data_dir}/{var_file}')
    return ds[var_name]

water_data = load_data(water_file, 'tcslw')
aod_data = load_data(aod_file, 'bcaod550')

time_dim = [dim for dim in water_data.dims if 'time' in dim.lower()][0]
lats = water_data.lat.values
lons = water_data.lon.values

# 修改tau_list，去除无时间滞后
tau_list = [1, 2, 3]  # 只保留1, 2, 3个月的时间滞后

results_dict = {
    'bcaod550_to_tcslw': {tau: np.zeros((len(lats), len(lons))) for tau in tau_list},
    'tcslw_to_bcaod550': {tau: np.zeros((len(lats), len(lons))) for tau in tau_list},
    'bcaod550_to_tcslw_sig': {tau: np.zeros((len(lats), len(lons))) for tau in tau_list},
    'tcslw_to_bcaod550_sig': {tau: np.zeros((len(lats), len(lons))) for tau in tau_list}
}

parcorr = ParCorr(significance='analytic')
pc_alpha = 0.05

def process_point(i, j, lat, lon):
    try:
        water_ts = water_data.sel(lat=lat, lon=lon).values
        aod_ts = aod_data.sel(lat=lat, lon=lon).values
        
        df = pd.DataFrame({
            'tcslw': water_ts,
            'bcaod550': aod_ts
        }, index=pd.to_datetime(water_data[time_dim].values))
        
        if df.isnull().any().any():
            df = df.interpolate().dropna()
            
        data = df.values
        dataframe = pp.DataFrame(data, datatime=df.index, var_names=['tcslw', 'bcaod550'])
        
        pcmci = PCMCI(
            dataframe=dataframe,
            cond_ind_test=parcorr,
            verbosity=0
        )
        
        results = pcmci.run_pcmci(tau_max=max(tau_list), pc_alpha=pc_alpha)
        
        return_dict = {}
        for tau in tau_list:
            return_dict[f'bcaod550_tcslw_{tau}'] = results['val_matrix'][1, 0, tau]
            return_dict[f'tcslw_bcaod550_{tau}'] = results['val_matrix'][0, 1, tau]
            return_dict[f'bcaod550_tcslw_sig_{tau}'] = results['p_matrix'][1, 0, tau] < pc_alpha
            return_dict[f'tcslw_bcaod550_sig_{tau}'] = results['p_matrix'][0, 1, tau] < pc_alpha
        
        return (i, j, return_dict)
        
    except Exception as e:
        print(f"Error at lat={lat}, lon={lon}: {str(e)}")
        return_dict = {f'{prefix}_{tau}': np.nan for prefix in ['bcaod550_tcslw', 'tcslw_bcaod550'] for tau in tau_list}
        return_dict.update({f'{prefix}_sig_{tau}': False for prefix in ['bcaod550_tcslw', 'tcslw_bcaod550'] for tau in tau_list})
        return (i, j, return_dict)

print("开始并行计算...")
# 增加并行计算的CPU利用效率
results = Parallel(n_jobs=-1, backend='threading', batch_size='auto', verbose=10)(
    delayed(process_point)(i, j, lat, lon)
    for i, lat in enumerate(tqdm(lats, desc="纬度进度"))
    for j, lon in enumerate(lons)
)

for i, j, result_dict in results:
    for tau in tau_list:
        results_dict['bcaod550_to_tcslw'][tau][i, j] = result_dict[f'bcaod550_tcslw_{tau}']
        results_dict['tcslw_to_bcaod550'][tau][i, j] = result_dict[f'tcslw_bcaod550_{tau}']
        results_dict['bcaod550_to_tcslw_sig'][tau][i, j] = result_dict[f'bcaod550_tcslw_sig_{tau}']
        results_dict['tcslw_to_bcaod550_sig'][tau][i, j] = result_dict[f'tcslw_bcaod550_sig_{tau}']

def save_to_single_nc():
    data_vars = {}
    for var_prefix in ['bcaod550_to_tcslw', 'tcslw_to_bcaod550']:
        for tau in tau_list:
            data_vars[f'{var_prefix}_tau{tau}'] = (['lat', 'lon'], results_dict[var_prefix][tau])
            data_vars[f'{var_prefix}_tau{tau}_sig'] = (['lat', 'lon'], results_dict[f'{var_prefix}_sig'][tau])
    
    ds = xr.Dataset(
        data_vars,
        coords={
            'lat': lats,
            'lon': lons
        }
    )
    ds.to_netcdf(f'{data_dir}/bc_results.nc')

save_to_single_nc()
print("处理完成！所有结果已保存为单个NC文件：bc_results.nc")