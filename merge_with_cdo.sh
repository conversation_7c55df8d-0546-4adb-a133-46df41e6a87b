#!/bin/bash

# 使用CDO合并MERRA-2 AOD数据文件
# 使用方法: bash merge_with_cdo.sh

echo "========================================="
echo "使用CDO合并MERRA-2 AOD数据文件"
echo "========================================="

# 配置路径
DOWNLOAD_DIR="/mnt/d/dataset/supercooled/merra2"
OUTPUT_FILE="/mnt/d/dataset/supercooled/80-24merra2.nc"
TEMP_DIR="/tmp/merra2_merge"

# 检查CDO是否安装
if ! command -v cdo &> /dev/null; then
    echo "CDO未安装，正在安装..."
    sudo apt update
    sudo apt install -y cdo
fi

echo "✓ CDO版本信息:"
cdo --version | head -1

# 检查下载目录是否存在
if [ ! -d "$DOWNLOAD_DIR" ]; then
    echo "错误: 下载目录不存在: $DOWNLOAD_DIR"
    echo "请先下载MERRA-2数据文件"
    exit 1
fi

# 查找所有.nc4文件
echo ""
echo "正在查找NetCDF文件..."
cd "$DOWNLOAD_DIR"

# 计算文件数量
nc_files=(*.nc4)
if [ ! -e "${nc_files[0]}" ]; then
    echo "错误: 在 $DOWNLOAD_DIR 中没有找到.nc4文件"
    echo "请确保已下载MERRA-2数据文件"
    exit 1
fi

file_count=${#nc_files[@]}
echo "找到 $file_count 个NetCDF文件"

# 显示文件大小信息
total_size=$(du -sh . | cut -f1)
echo "总文件大小: $total_size"

# 创建临时目录
mkdir -p "$TEMP_DIR"

# 检查第一个文件的结构
echo ""
echo "检查数据结构..."
first_file="${nc_files[0]}"
echo "示例文件: $first_file"

# 显示文件信息
cdo sinfon "$first_file" | head -10

echo ""
echo "开始合并过程..."

# 方法1: 直接使用cdo mergetime (推荐)
echo "方法1: 使用 cdo mergetime 合并文件..."

# 创建文件列表
file_list="$TEMP_DIR/file_list.txt"
ls -1 "$DOWNLOAD_DIR"/*.nc4 | sort > "$file_list"

echo "文件列表已创建: $file_list"
echo "文件数量: $(wc -l < "$file_list")"

# 使用CDO mergetime合并
echo "正在执行: cdo mergetime"
start_time=$(date +%s)

if cdo mergetime "$DOWNLOAD_DIR"/*.nc4 "$OUTPUT_FILE"; then
    end_time=$(date +%s)
    duration=$((end_time - start_time))
    
    echo "✓ 合并成功！"
    echo "✓ 耗时: ${duration} 秒"
    echo "✓ 输出文件: $OUTPUT_FILE"
    
    # 显示输出文件信息
    echo ""
    echo "合并后文件信息:"
    ls -lh "$OUTPUT_FILE"
    
    echo ""
    echo "数据集信息:"
    cdo sinfon "$OUTPUT_FILE"
    
    echo ""
    echo "时间范围:"
    cdo showtimestamp "$OUTPUT_FILE" | head -1
    echo "..."
    cdo showtimestamp "$OUTPUT_FILE" | tail -1
    
else
    echo "✗ 方法1失败，尝试方法2..."
    
    # 方法2: 分批合并 (如果文件太多)
    echo ""
    echo "方法2: 分批合并文件..."
    
    batch_size=50
    batch_count=$(( (file_count + batch_size - 1) / batch_size ))
    
    echo "将 $file_count 个文件分成 $batch_count 批，每批 $batch_size 个文件"
    
    batch_files=()
    
    for ((i=0; i<batch_count; i++)); do
        start_idx=$((i * batch_size))
        end_idx=$(( (i + 1) * batch_size - 1 ))
        
        if [ $end_idx -ge $file_count ]; then
            end_idx=$((file_count - 1))
        fi
        
        echo "处理批次 $((i+1))/$batch_count (文件 $((start_idx+1)) 到 $((end_idx+1)))"
        
        # 创建当前批次的文件列表
        batch_file="$TEMP_DIR/batch_${i}.nc"
        current_files=("${nc_files[@]:$start_idx:$((end_idx-start_idx+1))}")
        
        # 合并当前批次
        if cdo mergetime "${current_files[@]}" "$batch_file"; then
            batch_files+=("$batch_file")
            echo "✓ 批次 $((i+1)) 合并完成"
        else
            echo "✗ 批次 $((i+1)) 合并失败"
            exit 1
        fi
    done
    
    # 合并所有批次文件
    echo ""
    echo "合并所有批次文件..."
    if cdo mergetime "${batch_files[@]}" "$OUTPUT_FILE"; then
        echo "✓ 最终合并成功！"
        echo "✓ 输出文件: $OUTPUT_FILE"
        
        # 清理临时文件
        rm -f "${batch_files[@]}"
        
    else
        echo "✗ 最终合并失败"
        exit 1
    fi
fi

# 验证输出文件
echo ""
echo "验证输出文件..."

if [ -f "$OUTPUT_FILE" ]; then
    output_size=$(stat -c%s "$OUTPUT_FILE")
    if [ $output_size -gt 1000000 ]; then  # 大于1MB
        echo "✓ 输出文件验证通过"
        echo "✓ 文件大小: $(du -sh "$OUTPUT_FILE" | cut -f1)"
        
        # 显示详细信息
        echo ""
        echo "最终数据集详细信息:"
        echo "变量列表:"
        cdo showname "$OUTPUT_FILE"
        
        echo ""
        echo "维度信息:"
        cdo griddes "$OUTPUT_FILE" | head -10
        
        echo ""
        echo "时间步数:"
        cdo ntime "$OUTPUT_FILE"
        
        echo ""
        echo "时间范围:"
        echo "开始时间: $(cdo showtimestamp "$OUTPUT_FILE" | head -1 | awk '{print $1}')"
        echo "结束时间: $(cdo showtimestamp "$OUTPUT_FILE" | tail -1 | awk '{print $NF}')"
        
    else
        echo "✗ 输出文件太小，可能有问题"
        ls -lh "$OUTPUT_FILE"
    fi
else
    echo "✗ 输出文件不存在"
    exit 1
fi

# 清理临时目录
echo ""
echo "清理临时文件..."
rm -rf "$TEMP_DIR"

echo ""
echo "========================================="
echo "CDO合并完成！"
echo "输入目录: $DOWNLOAD_DIR"
echo "输出文件: $OUTPUT_FILE"
echo "文件数量: $file_count"
echo "========================================="

# 可选：压缩原始文件
read -p "是否要压缩原始.nc4文件以节省空间？(y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "正在压缩原始文件..."
    cd "$DOWNLOAD_DIR"
    tar -czf "original_nc4_files.tar.gz" *.nc4
    echo "✓ 原始文件已压缩为: $DOWNLOAD_DIR/original_nc4_files.tar.gz"
    
    read -p "是否要删除原始.nc4文件？(y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        rm -f *.nc4
        echo "✓ 原始.nc4文件已删除"
        echo "✓ 如需恢复，请解压: original_nc4_files.tar.gz"
    fi
fi

echo ""
echo "所有操作完成！"
