#!/usr/bin/env python3
"""
修复MERRA-2文件格式问题并合并
解决CDO无法读取的问题
"""

import xarray as xr
import numpy as np
from pathlib import Path
import glob
import os
import pandas as pd

def fix_netcdf_file(input_file, output_file):
    """修复单个NetCDF文件的格式问题"""
    try:
        # 读取原始文件
        ds = xr.open_dataset(input_file, decode_times=False)
        
        # 修复时间变量
        if 'time' in ds.variables:
            time_var = ds['time']
            
            # 移除有问题的属性
            time_attrs = dict(time_var.attrs)
            if 'valid_range' in time_attrs:
                del time_attrs['valid_range']
            
            # 重新创建时间变量
            ds['time'] = xr.Variable(time_var.dims, time_var.values, attrs=time_attrs)
        
        # 确保数据变量有正确的属性
        for var_name in ds.data_vars:
            var = ds[var_name]
            # 确保_FillValue是正确的类型
            if '_FillValue' in var.attrs:
                if var.dtype == np.float32:
                    ds[var_name].attrs['_FillValue'] = np.float32(var.attrs['_FillValue'])
                elif var.dtype == np.float64:
                    ds[var_name].attrs['_FillValue'] = np.float64(var.attrs['_FillValue'])
        
        # 保存修复后的文件
        ds.to_netcdf(output_file, format='NETCDF4_CLASSIC')
        ds.close()
        
        return True
        
    except Exception as e:
        print(f"修复文件失败 {input_file}: {e}")
        return False

def merge_files_with_xarray(input_dir, output_file):
    """使用xarray合并文件"""
    print("使用xarray合并文件...")
    
    # 查找所有.nc4文件
    nc4_files = sorted(glob.glob(os.path.join(input_dir, "*.nc4")))
    
    if len(nc4_files) == 0:
        print("没有找到.nc4文件")
        return False
    
    print(f"找到 {len(nc4_files)} 个文件")
    
    datasets = []
    valid_files = []
    
    for i, file_path in enumerate(nc4_files):
        try:
            print(f"加载文件 {i+1}/{len(nc4_files)}: {os.path.basename(file_path)}")
            
            # 尝试直接读取
            ds = xr.open_dataset(file_path, decode_times=True)
            
            # 检查数据完整性
            if 'AODANA' in ds.variables:
                aod_data = ds['AODANA']
                if aod_data.size > 0:
                    datasets.append(ds)
                    valid_files.append(file_path)
                else:
                    print(f"  跳过空文件: {os.path.basename(file_path)}")
                    ds.close()
            else:
                print(f"  跳过无AOD数据的文件: {os.path.basename(file_path)}")
                ds.close()
                
        except Exception as e:
            print(f"  无法读取文件 {os.path.basename(file_path)}: {e}")
    
    if len(datasets) == 0:
        print("没有有效的数据集可以合并")
        return False
    
    print(f"有效文件数: {len(datasets)}")
    
    try:
        print("正在合并数据集...")
        
        # 合并数据集
        merged_ds = xr.concat(datasets, dim='time')
        
        # 排序时间维度
        merged_ds = merged_ds.sortby('time')
        
        # 添加全局属性
        merged_ds.attrs['title'] = 'MERRA-2 AOD Data 1980-2024'
        merged_ds.attrs['source'] = 'NASA MERRA-2'
        merged_ds.attrs['created_by'] = 'Python xarray merge script'
        merged_ds.attrs['creation_date'] = pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')
        
        print(f"合并后的数据形状: {merged_ds.dims}")
        print(f"时间范围: {merged_ds.time.min().values} 到 {merged_ds.time.max().values}")
        
        # 保存合并后的文件
        print(f"保存到: {output_file}")
        merged_ds.to_netcdf(output_file, format='NETCDF4')
        
        # 关闭所有数据集
        for ds in datasets:
            ds.close()
        merged_ds.close()
        
        print("✅ 合并成功！")
        return True
        
    except Exception as e:
        print(f"合并过程出错: {e}")
        # 确保关闭所有数据集
        for ds in datasets:
            try:
                ds.close()
            except:
                pass
        return False

def convert_files_for_cdo(input_dir, temp_dir):
    """转换文件格式以便CDO可以读取"""
    print("转换文件格式以便CDO读取...")
    
    temp_path = Path(temp_dir)
    temp_path.mkdir(exist_ok=True)
    
    nc4_files = sorted(glob.glob(os.path.join(input_dir, "*.nc4")))
    converted_files = []
    
    for i, file_path in enumerate(nc4_files):
        try:
            filename = os.path.basename(file_path)
            output_file = temp_path / filename.replace('.nc4', '_fixed.nc')
            
            print(f"转换 {i+1}/{len(nc4_files)}: {filename}")
            
            if fix_netcdf_file(file_path, output_file):
                converted_files.append(str(output_file))
            
        except Exception as e:
            print(f"转换失败 {filename}: {e}")
    
    print(f"成功转换 {len(converted_files)} 个文件")
    return converted_files

def merge_with_cdo_fixed(converted_files, output_file):
    """使用CDO合并修复后的文件"""
    import subprocess
    
    try:
        print("使用CDO合并修复后的文件...")
        
        # 构建CDO命令
        cmd = ['cdo', 'mergetime'] + converted_files + [output_file]
        
        # 执行CDO命令
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ CDO合并成功！")
            return True
        else:
            print(f"❌ CDO合并失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"CDO合并出错: {e}")
        return False

def main():
    """主函数"""
    print("MERRA-2文件修复和合并工具")
    print("=" * 50)
    
    # 路径配置
    input_dir = r"D:\dataset\supercooled\merra2"
    output_file = r"D:\dataset\supercooled\80-24merra2.nc"
    temp_dir = r"D:\dataset\supercooled\temp_fixed"
    
    print(f"输入目录: {input_dir}")
    print(f"输出文件: {output_file}")
    print(f"临时目录: {temp_dir}")
    
    # 检查输入目录
    if not os.path.exists(input_dir):
        print(f"❌ 输入目录不存在: {input_dir}")
        return
    
    # 方法1: 直接使用xarray合并（推荐）
    print("\n方法1: 使用xarray直接合并")
    print("-" * 30)
    
    success = merge_files_with_xarray(input_dir, output_file)
    
    if success:
        # 验证输出文件
        try:
            ds = xr.open_dataset(output_file)
            file_size = os.path.getsize(output_file)
            
            print(f"\n✅ 合并成功验证:")
            print(f"文件大小: {file_size/1024/1024:.1f} MB")
            print(f"变量: {list(ds.variables.keys())}")
            print(f"维度: {dict(ds.dims)}")
            print(f"时间范围: {ds.time.min().values} 到 {ds.time.max().values}")
            
            ds.close()
            
            # 清理临时目录
            if os.path.exists(temp_dir):
                import shutil
                shutil.rmtree(temp_dir)
                print("临时文件已清理")
            
        except Exception as e:
            print(f"验证输出文件时出错: {e}")
    
    else:
        print("\n方法2: 修复文件格式后使用CDO")
        print("-" * 30)
        
        # 转换文件格式
        converted_files = convert_files_for_cdo(input_dir, temp_dir)
        
        if converted_files:
            # 尝试使用CDO合并
            cdo_success = merge_with_cdo_fixed(converted_files, output_file)
            
            if not cdo_success:
                print("\nCDO也失败了，回退到xarray方法...")
                success = merge_files_with_xarray(temp_dir, output_file)
        
        # 清理临时文件
        if os.path.exists(temp_dir):
            import shutil
            shutil.rmtree(temp_dir)
            print("临时文件已清理")

if __name__ == "__main__":
    main()
