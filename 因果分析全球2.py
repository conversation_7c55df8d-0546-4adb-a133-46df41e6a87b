# -*- coding: utf-8 -*-
"""
Created on Fri Mar 21 08:54:02 2025

@author: willz
"""

import xarray as xr
import numpy as np
import pandas as pd
import cartopy.crs as ccrs
import matplotlib.pyplot as plt
from tigramite import data_processing as pp
from tigramite import plotting as tp
from tigramite.pcmci import PCMCI
from tigramite.independence_tests.parcorr import ParCorr
from joblib import Parallel, delayed
from tqdm import tqdm
from scipy import stats

# 设置文件路径和参数
data_dir = r'D:\dataset\supercooled'
water_file = '03-23supercooledwater.nc'
aod_file = '03-23aod.nc'

# 加载数据
def load_data(var_file, var_name):
    ds = xr.open_dataset(f'{data_dir}/{var_file}')
    return ds[var_name]

water_data = load_data(water_file, 'tcslw')
aod_data = load_data(aod_file, 'aod550')

# 获取时间维度名称
time_dim = [dim for dim in water_data.dims if 'time' in dim.lower()][0]

# 获取全球格点信息
lats = water_data.lat.values
lons = water_data.lon.values

# 创建空数组存储结果
tcslw_to_aod = np.zeros((len(lats), len(lons)))
aod_to_tcslw = np.zeros((len(lats), len(lons)))
tcslw_to_aod_sig = np.zeros((len(lats), len(lons)))  # 显著性标记
aod_to_tcslw_sig = np.zeros((len(lats), len(lons)))  # 显著性标记

# PCMCI参数设置
parcorr = ParCorr(significance='analytic')
tau_max = 0  # 设置为0，表示无时间滞后
pc_alpha = 0.05  # 显著性水平

# 格点处理函数
def process_point(i, j, lat, lon):
    try:
        # 提取逐月时间序列
        water_ts = water_data.sel(lat=lat, lon=lon).values
        aod_ts = aod_data.sel(lat=lat, lon=lon).values
        
        # 转换为DataFrame
        df = pd.DataFrame({
            'tcslw': water_ts,
            'aod550': aod_ts
        }, index=pd.to_datetime(water_data[time_dim].values))
        
        # 处理缺失值
        if df.isnull().any().any():
            df = df.interpolate().dropna()
            
        # 使用tigramite计算因果关系
        dataframe = pp.DataFrame(df.values, datatime=df.index, var_names=['tcslw', 'aod550'])
        pcmci = PCMCI(
            dataframe=dataframe,
            cond_ind_test=parcorr,
            verbosity=0
        )
        
        # 运行PCMCI分析
        results = pcmci.run_pcmci(tau_max=tau_max, pc_alpha=pc_alpha)  # tau_max=0表示无时间滞后
        
        # 获取因果关系值和p值
        val_matrix = results['val_matrix']
        p_matrix = results['p_matrix']
        
        # 返回因果关系值和p值
        return i, j, val_matrix[0, 1, 0], val_matrix[1, 0, 0], p_matrix[0, 1, 0], p_matrix[1, 0, 0]
        
    except Exception as e:
        print(f"Error at lat={lat}, lon={lon}: {str(e)}")
        return i, j, np.nan, np.nan, 1.0, 1.0  # 返回p值为1.0表示不显著

# 并行计算主循环
print("开始并行计算...")
results = Parallel(n_jobs=-1)(delayed(process_point)(i, j, lat, lon)
                             for i, lat in enumerate(tqdm(lats, desc="纬度进度"))
                             for j, lon in enumerate(lons))

# 将结果填充到数组中
for i, j, tcslw_aod, aod_tcslw, tcslw_aod_p, aod_tcslw_p in results:
    tcslw_to_aod[i, j] = tcslw_aod
    aod_to_tcslw[i, j] = aod_tcslw
    tcslw_to_aod_sig[i, j] = tcslw_aod_p < pc_alpha  # 使用p值判断显著性
    aod_to_tcslw_sig[i, j] = aod_tcslw_p < pc_alpha  # 使用p值判断显著性

# 创建xarray Dataset并保存为NetCDF文件
def save_to_nc(data, sig_data, var_name, description):
    ds = xr.Dataset(
        {
            var_name: (["lat", "lon"], data),
            f"{var_name}_sig": (["lat", "lon"], sig_data)
        },
        coords={
            "lat": lats,
            "lon": lons
        }
    )
    ds[var_name].attrs = {
        'long_name': description,
        'units': 'MCI value',
        'valid_range': [-1.0, 1.0]
    }
    ds[f"{var_name}_sig"].attrs = {
        'long_name': f'Significance flag for {description}',
        'units': 'Boolean'
    }
    ds.attrs = {
        'title': 'Causal Analysis Results with Significance',
        'source': 'PCMCI method',
        'history': f'Created {pd.Timestamp.now()}'
    }
    ds.to_netcdf(f'{data_dir}/{var_name}_aod-tcslwcausal_effect_with_sig.nc')

# 保存结果
save_to_nc(tcslw_to_aod, tcslw_to_aod_sig, 'aodnodelay', 'Causal effect of TCSLW on AOD550')
save_to_nc(aod_to_tcslw, aod_to_tcslw_sig, 'tcslwnodelay', 'Causal effect of AOD550 on TCSLW')

# 移除绘图部分
print("处理完成！")