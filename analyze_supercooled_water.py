import numpy as np
import netCDF4 as nc
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
from scipy import stats
import os
import pickle

# 设置字体
plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def load_netcdf_data(file_path, variable_name):
    """加载NetCDF文件数据"""
    try:
        dataset = nc.Dataset(file_path, 'r')
        print(f"Available variables in {file_path}: {list(dataset.variables.keys())}")
        if variable_name in dataset.variables:
            data = dataset.variables[variable_name][:]
            dataset.close()
            return data
        else:
            print(f"Variable '{variable_name}' not found in file")
            dataset.close()
            return None
    except Exception as e:
        print(f"Error loading file {file_path}: {e}")
        return None

def calculate_average_and_classify(supercooled_file):
    """计算超冷水数据的平均值并分类格点"""
    print("Loading supercooled water data...")
    
    # 加载超冷水数据，变量名为tcslw
    supercooled_data = load_netcdf_data(supercooled_file, 'tcslw')
    if supercooled_data is None:
        return None, None, None
    
    # 计算平均值（如果数据是3D或4D，取时间平均）
    if len(supercooled_data.shape) == 3:  # [time, lat, lon]
        avg_supercooled = np.mean(supercooled_data, axis=0)
    else:
        avg_supercooled = supercooled_data
    
    print(f"Supercooled water data shape: {supercooled_data.shape}")
    print(f"Average shape: {avg_supercooled.shape}")
    print(f"Average range: {np.min(avg_supercooled):.6f} - {np.max(avg_supercooled):.6f}")
    
    # 分类格点
    threshold = 0.03737
    class1_mask = avg_supercooled <= threshold  # 第一类：小于等于0.03737
    class2_mask = avg_supercooled > threshold   # 第二类：大于0.03737
    
    print(f"Class 1 grid points: {np.sum(class1_mask)}")
    print(f"Class 2 grid points: {np.sum(class2_mask)}")
    
    return avg_supercooled, class1_mask, class2_mask

def calculate_ratio_pdf(tcw_tciw_file, tcslw_file, class1_mask, class2_mask):
    """计算tciw/(tcslw+tciw)的PDF"""
    print("Loading TCIW and TCSLW data...")
    
    # 加载tciw数据
    dataset = nc.Dataset(tcw_tciw_file, 'r')
    tciw_data = dataset.variables['tciw'][:]
    dataset.close()
    
    # 加载tcslw数据
    dataset = nc.Dataset(tcslw_file, 'r')
    tcslw_data = dataset.variables['tcslw'][:]
    dataset.close()
    
    print(f"TCIW data shape: {tciw_data.shape}")
    print(f"TCSLW data shape: {tcslw_data.shape}")
    
    # 如果数据是3D，取时间平均
    if len(tciw_data.shape) == 3:
        tciw_avg = np.mean(tciw_data, axis=0)
    else:
        tciw_avg = tciw_data
        
    if len(tcslw_data.shape) == 3:
        tcslw_avg = np.mean(tcslw_data, axis=0)
    else:
        tcslw_avg = tcslw_data
    
    # 计算比率 tciw/(tcslw+tciw)
    total_water = tcslw_avg + tciw_avg
    valid_mask = total_water > 0
    ratio = np.zeros_like(tciw_avg)
    ratio[valid_mask] = tciw_avg[valid_mask] / total_water[valid_mask]
    
    print(f"Ratio range: {np.min(ratio):.6f} - {np.max(ratio):.6f}")
    
    # 注意：此处未做空间插值，假设mask和数据shape一致，否则需插值
    if class1_mask.shape != ratio.shape:
        print(f"Warning: mask shape {class1_mask.shape} and data shape {ratio.shape} do not match!")
        # 这里可以加插值代码
        return None, None
    
    # 提取两类区域的比率值
    class1_ratio = ratio[class1_mask & valid_mask]
    class2_ratio = ratio[class2_mask & valid_mask]
    
    print(f"Class 1 valid ratio count: {len(class1_ratio)}")
    print(f"Class 2 valid ratio count: {len(class2_ratio)}")
    
    return class1_ratio, class2_ratio

def save_results(class1_ratio, class2_ratio, avg_supercooled, class1_mask, class2_mask, filename='analysis_results.pkl'):
    """保存分析结果到文件"""
    results = {
        'class1_ratio': class1_ratio,
        'class2_ratio': class2_ratio,
        'avg_supercooled': avg_supercooled,
        'class1_mask': class1_mask,
        'class2_mask': class2_mask,
        'class1_count': len(class1_ratio),
        'class2_count': len(class2_ratio),
        'threshold': 0.03737
    }
    
    with open(filename, 'wb') as f:
        pickle.dump(results, f)
    
    print(f"Results saved to {filename}")
    print(f"Class 1 data points: {len(class1_ratio)}")
    print(f"Class 2 data points: {len(class2_ratio)}")

def load_results(filename='analysis_results.pkl'):
    """从文件加载分析结果"""
    try:
        with open(filename, 'rb') as f:
            results = pickle.load(f)
        print(f"Results loaded from {filename}")
        return results
    except FileNotFoundError:
        print(f"File {filename} not found. Please run the analysis first.")
        return None

def plot_pdf_comparison(class1_ratio, class2_ratio, save_plot=True):
    """绘制PDF对比图"""
    plt.figure(figsize=(12, 8))
    
    # 设置直方图参数
    bins = 50
    alpha = 0.7
    
    # 绘制第一类区域的PDF
    plt.hist(class1_ratio, bins=bins, alpha=alpha, density=True, 
             label=f'Class 1 (≤0.03737, n={len(class1_ratio)})', 
             color='blue', edgecolor='black')
    
    # 绘制第二类区域的PDF
    plt.hist(class2_ratio, bins=bins, alpha=alpha, density=True, 
             label=f'Class 2 (>0.03737, n={len(class2_ratio)})', 
             color='red', edgecolor='black')
    
    # 添加核密度估计
    if len(class1_ratio) > 0:
        kde1 = stats.gaussian_kde(class1_ratio)
        x_range = np.linspace(min(class1_ratio), max(class1_ratio), 200)
        plt.plot(x_range, kde1(x_range), 'b-', linewidth=2, label='Class 1 KDE')
    
    if len(class2_ratio) > 0:
        kde2 = stats.gaussian_kde(class2_ratio)
        x_range = np.linspace(min(class2_ratio), max(class2_ratio), 200)
        plt.plot(x_range, kde2(x_range), 'r-', linewidth=2, label='Class 2 KDE')
    
    plt.xlabel('TCIW/(TCLW+TCIW) Ratio')
    plt.ylabel('Probability Density')
    plt.title('PDF Comparison of TCIW/(TCLW+TCIW) Ratio for Two Classes')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 添加统计信息
    if len(class1_ratio) > 0 and len(class2_ratio) > 0:
        mean1, std1 = np.mean(class1_ratio), np.std(class1_ratio)
        mean2, std2 = np.mean(class2_ratio), np.std(class2_ratio)
        
        stats_text = f'Class 1: Mean={mean1:.4f}, Std={std1:.4f}\n'
        stats_text += f'Class 2: Mean={mean2:.4f}, Std={std2:.4f}'
        
        plt.text(0.02, 0.98, stats_text, transform=plt.gca().transAxes, 
                verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    
    plt.tight_layout()
    
    if save_plot:
        plt.savefig('tciw_ratio_pdf_comparison.png', dpi=300, bbox_inches='tight')
        print("Plot saved as tciw_ratio_pdf_comparison.png")
    
    plt.show()

def plot_from_saved_data(filename='analysis_results.pkl'):
    """从保存的数据文件绘制图形"""
    results = load_results(filename)
    if results is None:
        return
    
    class1_ratio = results['class1_ratio']
    class2_ratio = results['class2_ratio']
    
    print("Plotting from saved data...")
    plot_pdf_comparison(class1_ratio, class2_ratio)

def main():
    """主函数"""
    # 文件路径
    supercooled_file = r"D:\dataset\supercooled\tciw\79-24tcslw.nc"
    tcw_tciw_file = r"D:\dataset\supercooled\tciw\tcw-tciw-79-24.nc"
    tcslw_file = r"D:\dataset\supercooled\tciw\79-24tcslw.nc"  # 使用相同的文件
    
    # 检查文件是否存在
    if not os.path.exists(supercooled_file):
        print(f"Error: File not found {supercooled_file}")
        return
    
    if not os.path.exists(tcw_tciw_file):
        print(f"Error: File not found {tcw_tciw_file}")
        return
        
    if not os.path.exists(tcslw_file):
        print(f"Error: File not found {tcslw_file}")
        return
    
    print("Starting supercooled water analysis...")
    
    # 步骤1：计算超冷水平均值并分类格点
    avg_supercooled, class1_mask, class2_mask = calculate_average_and_classify(supercooled_file)
    
    if avg_supercooled is None:
        print("Unable to process supercooled water data")
        return
    
    # 步骤2：计算两类区域的tciw/(tcslw+tciw)比率PDF
    class1_ratio, class2_ratio = calculate_ratio_pdf(tcw_tciw_file, tcslw_file, class1_mask, class2_mask)
    
    if class1_ratio is None or class2_ratio is None:
        print("Unable to calculate ratio PDF")
        return
    
    # 步骤3：保存分析结果到文件
    save_results(class1_ratio, class2_ratio, avg_supercooled, class1_mask, class2_mask)
    
    # 步骤4：绘制PDF对比图
    plot_pdf_comparison(class1_ratio, class2_ratio)
    
    print("Analysis completed!")

if __name__ == "__main__":
    main() 