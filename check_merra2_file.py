#!/usr/bin/env python3
"""
检查MERRA-2合并文件的问题
"""

import xarray as xr
import numpy as np
from pathlib import Path
import os

def check_file_details(file_path):
    """详细检查文件内容"""
    print(f"检查文件: {file_path}")
    print("=" * 60)
    
    if not os.path.exists(file_path):
        print("❌ 文件不存在！")
        return
    
    # 文件大小
    file_size = os.path.getsize(file_path)
    print(f"📁 文件大小: {file_size:,} bytes ({file_size/1024/1024:.1f} MB)")
    
    try:
        # 打开数据集
        ds = xr.open_dataset(file_path)
        
        print(f"\n📊 数据集信息:")
        print(f"变量: {list(ds.variables.keys())}")
        print(f"维度: {dict(ds.dims)}")
        
        # 检查时间维度
        if 'time' in ds.dims:
            time_size = ds.dims['time']
            print(f"⏰ 时间步数: {time_size}")
            print(f"时间范围: {ds.time.min().values} 到 {ds.time.max().values}")
            
            # 计算预期的时间步数 (1980-2024年，每年12个月)
            expected_months = (2024 - 1980 + 1) * 12
            print(f"预期时间步数: {expected_months} (1980-2024年，每年12个月)")
            
            if time_size < expected_months * 0.8:  # 如果少于预期的80%
                print(f"⚠️  警告: 时间步数可能不完整！")
        
        # 检查空间维度
        if 'lat' in ds.dims and 'lon' in ds.dims:
            lat_size = ds.dims['lat']
            lon_size = ds.dims['lon']
            print(f"🌍 空间维度: {lat_size} x {lon_size}")
            
            # MERRA-2的标准分辨率
            expected_lat = 361  # 0.5度分辨率
            expected_lon = 576  # 0.625度分辨率
            print(f"MERRA-2标准分辨率: {expected_lat} x {expected_lon}")
            
            if lat_size != expected_lat or lon_size != expected_lon:
                print(f"⚠️  注意: 空间分辨率与标准MERRA-2不同")
        
        # 检查数据变量
        data_vars = [var for var in ds.variables.keys() 
                    if var not in ['time', 'lat', 'lon']]
        print(f"\n📈 数据变量: {data_vars}")
        
        for var in data_vars:
            var_data = ds[var]
            print(f"\n变量 '{var}':")
            print(f"  形状: {var_data.shape}")
            print(f"  数据类型: {var_data.dtype}")
            
            # 检查是否有有效数据
            if hasattr(var_data, 'values'):
                values = var_data.values
                valid_count = np.sum(~np.isnan(values))
                total_count = values.size
                valid_percent = (valid_count / total_count) * 100
                
                print(f"  有效数据点: {valid_count:,} / {total_count:,} ({valid_percent:.1f}%)")
                
                if valid_count > 0:
                    print(f"  数值范围: {np.nanmin(values):.6f} 到 {np.nanmax(values):.6f}")
                    print(f"  平均值: {np.nanmean(values):.6f}")
                else:
                    print(f"  ❌ 没有有效数据！")
        
        # 估算合理的文件大小
        if 'time' in ds.dims and 'lat' in ds.dims and 'lon' in ds.dims:
            total_elements = ds.dims['time'] * ds.dims['lat'] * ds.dims['lon']
            # 假设float32 (4字节) + 压缩比约50%
            estimated_size = total_elements * 4 * 0.5
            print(f"\n💾 估算文件大小: {estimated_size/1024/1024:.1f} MB")
            
            if file_size < estimated_size * 0.1:  # 如果实际大小小于估算的10%
                print(f"❌ 文件大小异常小！可能存在问题")
            elif file_size < estimated_size * 0.5:
                print(f"⚠️  文件大小偏小，可能数据不完整")
            else:
                print(f"✅ 文件大小正常")
        
        ds.close()
        
    except Exception as e:
        print(f"❌ 读取文件时出错: {e}")

def check_original_files(download_dir):
    """检查原始下载文件"""
    print(f"\n检查原始文件目录: {download_dir}")
    print("=" * 60)
    
    if not os.path.exists(download_dir):
        print("❌ 下载目录不存在！")
        return
    
    # 查找.nc4文件
    nc4_files = list(Path(download_dir).glob("*.nc4"))
    print(f"📁 找到 {len(nc4_files)} 个.nc4文件")
    
    if len(nc4_files) == 0:
        print("❌ 没有找到.nc4文件！")
        return
    
    # 检查文件大小分布
    file_sizes = []
    for file_path in nc4_files[:10]:  # 检查前10个文件
        size = file_path.stat().st_size
        file_sizes.append(size)
        print(f"  {file_path.name}: {size:,} bytes ({size/1024/1024:.1f} MB)")
    
    if file_sizes:
        avg_size = np.mean(file_sizes)
        total_expected = avg_size * len(nc4_files)
        print(f"\n📊 统计:")
        print(f"平均文件大小: {avg_size/1024/1024:.1f} MB")
        print(f"预期总大小: {total_expected/1024/1024/1024:.1f} GB")
    
    # 检查一个示例文件的内容
    if nc4_files:
        print(f"\n检查示例文件: {nc4_files[0].name}")
        try:
            ds = xr.open_dataset(nc4_files[0])
            print(f"变量: {list(ds.variables.keys())}")
            print(f"维度: {dict(ds.dims)}")
            
            # 检查AOD变量
            aod_vars = [var for var in ds.variables.keys() if 'aod' in var.lower()]
            if aod_vars:
                print(f"AOD变量: {aod_vars}")
                for var in aod_vars:
                    print(f"  {var}: {ds[var].shape}")
            else:
                print("⚠️  没有找到AOD变量")
            
            ds.close()
            
        except Exception as e:
            print(f"❌ 读取示例文件失败: {e}")

def main():
    """主函数"""
    print("MERRA-2文件检查工具")
    print("=" * 60)
    
    # 检查合并后的文件
    merged_files = [
        "/mnt/d/dataset/supercooled/80-24aodmerra2.nc",
        "/mnt/d/dataset/supercooled/80-24merra2.nc"
    ]
    
    for file_path in merged_files:
        if os.path.exists(file_path):
            check_file_details(file_path)
            print("\n" + "="*60 + "\n")
    
    # 检查原始文件
    download_dir = "/mnt/d/dataset/supercooled/merra2"
    check_original_files(download_dir)
    
    # 给出建议
    print("\n🔧 问题诊断和建议:")
    print("=" * 60)
    print("1. 如果合并文件太小，可能原因:")
    print("   - 下载的文件不完整")
    print("   - 合并过程中出现错误")
    print("   - 数据被意外截断")
    print("   - 只合并了部分文件")
    
    print("\n2. 解决方案:")
    print("   - 重新检查下载的文件完整性")
    print("   - 使用CDO重新合并: cdo mergetime *.nc4 output.nc")
    print("   - 检查磁盘空间是否足够")
    print("   - 验证原始文件的数据内容")

if __name__ == "__main__":
    main()
