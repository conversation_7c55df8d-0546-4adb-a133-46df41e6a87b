import xarray as xr
import matplotlib.pyplot as plt
import numpy as np
from datetime import datetime

# 文件路径
file_path = r'D:\dataset\slw\2024112719\SPECM_W02_Ka02_20241127190004_VPT.nc'

# 打开NetCDF文件
ds = xr.open_dataset(file_path)

# 获取时间信息
year = int(ds['Year'][0].values)
month = int(ds['Month'][0].values)
day = int(ds['Day'][0].values)
hour = int(ds['Hour'][0].values)
minute = int(ds['Minute'][0].values)
second = int(ds['Second'][0].values)
time_str = f"{year:04d}-{month:02d}-{day:02d} {hour:02d}:{minute:02d}:{second:02d}"

# 画功率谱
spec_vars = [
    ('PowerSpectrumW1', 'W1'),
    ('PowerSpectrumW2', 'W2'),
    ('PowerSpectrumKa1', 'Ka1'),
    ('PowerSpectrumKa2', 'Ka2'),
]

for var, label in spec_vars:
    data = ds[var][0].values  # shape: (BinNumber, PointNumber)
    n_bins = data.shape[0]
    n_points = data.shape[1]
    plt.figure(figsize=(10, 6))
    for i in range(0, n_bins, max(1, n_bins // 10)):
        plt.plot(np.arange(n_points), data[i, :], label=f'Bin {i}')
    plt.title(f'{label} Power Spectrum\n{time_str}')
    plt.xlabel('Spectrum Point')
    plt.ylabel('Power')
    plt.legend()
    plt.tight_layout()
    plt.show()

ds.close() 