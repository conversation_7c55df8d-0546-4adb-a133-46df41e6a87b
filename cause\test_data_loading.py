import xarray as xr
import numpy as np

print("开始测试数据加载...")

try:
    print("1. 加载因果效应数据...")
    causal_ds = xr.open_dataset('D:/dataset/supercooled/aod_to_tcslw_multi_tau_causal_effect.nc')
    print(f"   因果效应数据形状: {causal_ds.dims}")
    print(f"   变量: {list(causal_ds.data_vars.keys())}")
    
    print("2. 加载AOD数据...")
    aod_ds = xr.open_dataset('D:/dataset/supercooled/aodmean.nc')
    print(f"   AOD数据形状: {aod_ds.dims}")
    print(f"   变量: {list(aod_ds.data_vars.keys())}")
    
    print("3. 加载TCSLW数据...")
    tcslw_ds = xr.open_dataset('D:/dataset/supercooled/tciw/79-24tcslw.nc')
    print(f"   TCSLW数据形状: {tcslw_ds.dims}")
    print(f"   变量: {list(tcslw_ds.data_vars.keys())}")
    
    print("4. 加载TCIW数据...")
    tciw_ds = xr.open_dataset('D:/dataset/supercooled/tciw/tcw-tciw-79-24.nc')
    print(f"   TCIW数据形状: {tciw_ds.dims}")
    print(f"   变量: {list(tciw_ds.data_vars.keys())}")
    
    print("所有数据加载成功！")
    
    # 关闭数据集
    causal_ds.close()
    aod_ds.close()
    tcslw_ds.close()
    tciw_ds.close()
    
except Exception as e:
    print(f"错误: {e}")
    import traceback
    traceback.print_exc()
