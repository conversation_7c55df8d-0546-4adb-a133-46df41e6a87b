import xarray as xr
import numpy as np
import cartopy.crs as ccrs
import matplotlib.pyplot as plt

# 设置文件路径
data_dir = r'D:\dataset\supercooled'

# 读取已保存的结果文件
def load_results(var_name, tau=None):
    if tau is None:
        # 读取无时间滞后结果
        ds = xr.open_dataset(f'{data_dir}/{var_name}_instant.nc')
        return ds[var_name].values, ds[f'{var_name}_sig'].values, ds['lat'].values, ds['lon'].values
    else:
        # 读取有时间滞后结果
        ds = xr.open_dataset(f'{data_dir}/{var_name}_multi_tau.nc')
        return ds[f'{var_name}_tau{tau}'].values, ds[f'{var_name}_tau{tau}_sig'].values, ds['lat'].values, ds['lon'].values

# 绘制空间分布图
def plot_spatial(data, sig_data, title, save_name, tau=None):
    plt.figure(figsize=(12, 8))
    ax = plt.axes(projection=ccrs.PlateCarree())
    ax.coastlines()
    
    im = ax.pcolormesh(lons, lats, data, cmap='RdBu_r',
                      transform=ccrs.PlateCarree(), vmin=-1, vmax=1)
    plt.colorbar(im, ax=ax, label='Causal Strength', orientation='horizontal', pad=0.05)
    
    # 5度间隔显示显著点
    lat_grid = np.arange(0, len(lats), 5)
    lon_grid = np.arange(0, len(lons), 5)
    
    for lat_idx in lat_grid:
        for lon_idx in lon_grid:
            if sig_data[lat_idx, lon_idx]:
                ax.scatter(lons[lon_idx], lats[lat_idx],
                           color='black', s=3, transform=ccrs.PlateCarree())
    
    if tau is None:
        ax.set_title(f'{title} (Instantaneous)')
        plt.savefig(f'{data_dir}/{save_name}_instant.png', dpi=300, bbox_inches='tight')
    else:
        ax.set_title(f'{title} (Lag {tau} months)')
        plt.savefig(f'{data_dir}/{save_name}_tau{tau}.png', dpi=300, bbox_inches='tight')
    plt.show()

# 绘制所有结果图
print("Start plotting...")

# 绘制无时间滞后结果
aod_tcslw_inst, aod_sig_inst, lats, lons = load_results('aod550_to_tcslw')
plot_spatial(aod_tcslw_inst, aod_sig_inst,
            'Instantaneous Causal Effect of AOD550 on TCSLW', 'aod550_to_tcslw')

tcslw_aod_inst, tcslw_sig_inst, _, _ = load_results('tcslw_to_aod550')
plot_spatial(tcslw_aod_inst, tcslw_sig_inst,
            'Instantaneous Causal Effect of TCSLW on AOD550', 'tcslw_to_aod550')

# 绘制有时间滞后结果
for tau in [1, 2, 3]:
    # 读取AOD→TCSLW数据
    aod_to_tcslw, aod_sig, lats, lons = load_results('aod550_to_tcslw', tau)
    plot_spatial(aod_to_tcslw, aod_sig,
                'Causal Effect of AOD550 on TCSLW', 'aod550_to_tcslw', tau)
    
    # 读取TCSLW→AOD数据
    tcslw_to_aod, tcslw_sig, _, _ = load_results('tcslw_to_aod550', tau)
    plot_spatial(tcslw_to_aod, tcslw_sig,
                'Causal Effect of TCSLW on AOD550', 'tcslw_to_aod550', tau)

print("Processing completed!")