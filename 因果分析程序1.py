# -*- coding: utf-8 -*-
"""
Created on Fri Mar 21 08:54:02 2025

@author: willz
"""

import xarray as xr
import numpy as np
import pandas as pd
from tigramite import data_processing as pp
from tigramite import plotting as tp
from tigramite.pcmci import PCMCI
from tigramite.independence_tests.parcorr import ParCorr

# 设置文件路径和参数
data_dir = r'D:\dataset\supercooled'
water_file = '03-23supercooledwater.nc'
aod_file = '03-23aod.nc'
lat = 45.0  # 请替换为您的目标纬度
lon = 30.0  # 请替换为您的目标经度

# 读取NetCDF数据
def load_data(var_file, var_name):
    ds = xr.open_dataset(f'{data_dir}/{var_file}')
    # 假设时间维度名为'time'，空间维度为'lat'和'lon'
    return ds[var_name].sel(lat=lat, lon=lon, method='nearest')

# 加载两个变量数据
water_data = load_data(water_file, 'tcslw')  # 请确认变量名称
aod_data = load_data(aod_file, 'aod550')  # 请确认变量名称

# 打印water_data的信息，查看时间维度的名称
print(water_data)

# 找到实际的时间维度名称
time_dim = None
for dim in water_data.dims:
    if 'time' in dim.lower():
        time_dim = dim
        break

if time_dim is None:
    raise ValueError("未找到时间维度。请检查数据。")

# 创建包含两个变量的DataFrame
df = pd.DataFrame({
    'tcslw': water_data.values,
    'aod550': aod_data.values
}, index=pd.to_datetime(water_data[time_dim].values))

# 检查缺失值
if df.isnull().any().any():
    df = df.interpolate().dropna()

# 转换为tigramite数据结构
data = df.values
var_names = df.columns.tolist()
dataframe = pp.DataFrame(data, datatime=df.index, var_names=var_names)

# 设置PCMCI参数
parcorr = ParCorr(significance='analytic')
pcmci = PCMCI(
    dataframe=dataframe,
    cond_ind_test=parcorr,
    verbosity=1
)

# 运行因果发现
tau_max = 6  # 最大时间滞后（月）
pc_alpha = 0.2  # 显著性水平
results = pcmci.run_pcmci(tau_max=tau_max, pc_alpha=pc_alpha)

# 绘制因果图
tp.plot_graph(
    val_matrix=results['val_matrix'],
    graph=results['graph'],
    var_names=var_names,
    link_colorbar_label='Cross-MCI',
    node_colorbar_label='Auto-MCI',
    figsize=(10, 6)
)

# 保存图形
import matplotlib.pyplot as plt
plt.savefig(f'{data_dir}/causal_network.png', dpi=300, bbox_inches='tight')
plt.show()

# 输出结果摘要
print("显著因果链接：")
for i, j, tau in zip(*np.where(results['graph'] != "")):
    if tau != 0:
        print(f"{var_names[i]} --{tau}--> {var_names[j]}")
        print(f"   MCI = {results['val_matrix'][i, j, abs(tau)]:.3f}")
        print(f"   p-value = {results['p_matrix'][i, j, abs(tau)]:.4f}")