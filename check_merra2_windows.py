#!/usr/bin/env python3
"""
检查MERRA-2合并文件的问题 (Windows版本)
"""

import xarray as xr
import numpy as np
from pathlib import Path
import os
import glob

def check_file_details(file_path):
    """详细检查文件内容"""
    print(f"检查文件: {file_path}")
    print("=" * 60)
    
    if not os.path.exists(file_path):
        print("❌ 文件不存在！")
        return False
    
    # 文件大小
    file_size = os.path.getsize(file_path)
    print(f"📁 文件大小: {file_size:,} bytes ({file_size/1024/1024:.1f} MB)")
    
    try:
        # 打开数据集
        ds = xr.open_dataset(file_path)
        
        print(f"\n📊 数据集信息:")
        print(f"变量: {list(ds.variables.keys())}")
        print(f"维度: {dict(ds.dims)}")
        
        # 检查时间维度
        if 'time' in ds.dims:
            time_size = ds.dims['time']
            print(f"⏰ 时间步数: {time_size}")
            print(f"时间范围: {ds.time.min().values} 到 {ds.time.max().values}")
            
            # 计算预期的时间步数 (1980-2024年，每年12个月)
            expected_months = (2024 - 1980 + 1) * 12
            print(f"预期时间步数: {expected_months} (1980-2024年，每年12个月)")
            
            if time_size < expected_months * 0.8:  # 如果少于预期的80%
                print(f"⚠️  警告: 时间步数可能不完整！实际{time_size}，预期{expected_months}")
        
        # 检查空间维度
        if 'lat' in ds.dims and 'lon' in ds.dims:
            lat_size = ds.dims['lat']
            lon_size = ds.dims['lon']
            print(f"🌍 空间维度: {lat_size} x {lon_size}")
        
        # 检查数据变量
        data_vars = [var for var in ds.variables.keys() 
                    if var not in ['time', 'lat', 'lon']]
        print(f"\n📈 数据变量: {data_vars}")
        
        for var in data_vars:
            var_data = ds[var]
            print(f"\n变量 '{var}':")
            print(f"  形状: {var_data.shape}")
            print(f"  数据类型: {var_data.dtype}")
            
            # 检查是否有有效数据
            if hasattr(var_data, 'values'):
                try:
                    # 只检查一小部分数据以避免内存问题
                    sample_data = var_data.isel(time=slice(0, min(10, var_data.shape[0])))
                    values = sample_data.values
                    valid_count = np.sum(~np.isnan(values))
                    total_count = values.size
                    valid_percent = (valid_count / total_count) * 100
                    
                    print(f"  样本有效数据: {valid_count:,} / {total_count:,} ({valid_percent:.1f}%)")
                    
                    if valid_count > 0:
                        print(f"  样本数值范围: {np.nanmin(values):.6f} 到 {np.nanmax(values):.6f}")
                    else:
                        print(f"  ❌ 样本中没有有效数据！")
                except Exception as e:
                    print(f"  ⚠️  无法检查数据内容: {e}")
        
        ds.close()
        return True
        
    except Exception as e:
        print(f"❌ 读取文件时出错: {e}")
        return False

def check_original_files():
    """检查原始下载文件"""
    # 可能的下载目录
    possible_dirs = [
        r"D:\dataset\supercooled\merra2",
        r"D:\dataset\supercooled",
        r".\merra2",
        r"."
    ]
    
    download_dir = None
    for dir_path in possible_dirs:
        if os.path.exists(dir_path):
            nc4_files = glob.glob(os.path.join(dir_path, "*.nc4"))
            if nc4_files:
                download_dir = dir_path
                break
    
    if download_dir is None:
        print("❌ 没有找到包含.nc4文件的目录！")
        return
    
    print(f"\n检查原始文件目录: {download_dir}")
    print("=" * 60)
    
    # 查找.nc4文件
    nc4_files = glob.glob(os.path.join(download_dir, "*.nc4"))
    print(f"📁 找到 {len(nc4_files)} 个.nc4文件")
    
    if len(nc4_files) == 0:
        print("❌ 没有找到.nc4文件！")
        return
    
    # 检查文件大小分布
    file_sizes = []
    print("\n前10个文件:")
    for i, file_path in enumerate(nc4_files[:10]):
        size = os.path.getsize(file_path)
        file_sizes.append(size)
        filename = os.path.basename(file_path)
        print(f"  {filename}: {size:,} bytes ({size/1024/1024:.1f} MB)")
    
    if file_sizes:
        avg_size = np.mean(file_sizes)
        total_expected = avg_size * len(nc4_files)
        print(f"\n📊 统计:")
        print(f"平均文件大小: {avg_size/1024/1024:.1f} MB")
        print(f"预期总大小: {total_expected/1024/1024/1024:.1f} GB")
    
    # 检查一个示例文件的内容
    print(f"\n检查示例文件: {os.path.basename(nc4_files[0])}")
    try:
        ds = xr.open_dataset(nc4_files[0])
        print(f"变量: {list(ds.variables.keys())}")
        print(f"维度: {dict(ds.dims)}")
        
        # 检查AOD变量
        aod_vars = [var for var in ds.variables.keys() if 'aod' in var.lower()]
        if aod_vars:
            print(f"AOD变量: {aod_vars}")
            for var in aod_vars:
                print(f"  {var}: {ds[var].shape}")
        else:
            print("⚠️  没有找到AOD变量")
            print("所有变量:", list(ds.variables.keys()))
        
        ds.close()
        
    except Exception as e:
        print(f"❌ 读取示例文件失败: {e}")

def main():
    """主函数"""
    print("MERRA-2文件检查工具")
    print("=" * 60)
    
    # 检查合并后的文件
    possible_merged_files = [
        r"D:\dataset\supercooled\80-24aodmerra2.nc",
        r"D:\dataset\supercooled\80-24merra2.nc",
        r"80-24aodmerra2.nc",
        r"80-24merra2.nc"
    ]
    
    found_merged = False
    for file_path in possible_merged_files:
        if os.path.exists(file_path):
            found_merged = True
            check_file_details(file_path)
            print("\n" + "="*60 + "\n")
    
    if not found_merged:
        print("❌ 没有找到合并后的文件！")
        print("查找的路径:")
        for path in possible_merged_files:
            print(f"  {path}")
    
    # 检查原始文件
    check_original_files()
    
    # 给出建议
    print("\n🔧 问题诊断和建议:")
    print("=" * 60)
    print("1. 如果合并文件太小，可能原因:")
    print("   - 下载的文件不完整或损坏")
    print("   - 合并过程中出现错误")
    print("   - 只合并了部分文件")
    print("   - 数据压缩率很高")
    
    print("\n2. 正常的MERRA-2文件大小参考:")
    print("   - 单个月文件: 20-50 MB")
    print("   - 45年数据(540个月): 10-25 GB")
    print("   - 如果小于1GB，很可能有问题")
    
    print("\n3. 解决方案:")
    print("   - 检查原始文件是否完整下载")
    print("   - 重新合并文件")
    print("   - 验证网络下载是否成功")

if __name__ == "__main__":
    main()
