import xarray as xr
import numpy as np
import matplotlib.pyplot as plt
from scipy.stats import gaussian_kde
import matplotlib as mpl
import seaborn as sns

def analyze_and_plot_aod_based_causal_impact():
    """
    基于AOD浓度分析因果效应与云的冰相态的关系：
    1. 从aod_to_tcslw_multi_tau_causal_effect.nc中提取因果效应
    2. 从aodmean.nc中提取AOD浓度数据
    3. 根据AOD阈值(LOESS图中y=0对应的x值)创建高低AOD区域掩码
    4. 从79-24tcslw.nc计算 tciw/(tcslw+tciw) 的时间平均值
    5. 绘制高低AOD区域的PDF对比图
    """
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
    plt.rcParams['axes.unicode_minus'] = False
    
    # AOD阈值 (从LOESS图中y=0对应的x值，需要运行上面的代码获取)
    AOD_THRESHOLD = 0.0829  # 这个值需要从find_zero_crossing()函数获取
    
    # --- 1. 加载数据 ---
    try:
        # 加载因果效应数据
        causal_ds = xr.open_dataset('D:/dataset/supercooled/aod_to_tcslw_multi_tau_causal_effect.nc')
        # 加载AOD数据
        aod_ds = xr.open_dataset('D:/dataset/supercooled/aodmean.nc')
        # 加载云水数据 - 只加载需要的变量
        print("加载TCSLW数据...")
        tcslw_ds = xr.open_dataset('D:/dataset/supercooled/tciw/79-24tcslw.nc')
        print("加载TCIW数据...")
        # 只加载tciw变量，不加载tclw
        tciw_ds = xr.open_dataset('D:/dataset/supercooled/tciw/tcw-tciw-79-24.nc')[['tciw']]
        print("数据文件加载成功。")
        print("TCSLW数据变量:", list(tcslw_ds.data_vars.keys()))
        print("TCIW数据变量:", list(tciw_ds.data_vars.keys()))
        print("✓ 只使用TCSLW和TCIW，分母 = TCSLW + TCIW")
        
    except FileNotFoundError as e:
        print(f"错误: {e}")
        return

    # --- 2. 数据插值和对齐 ---
    print("开始AOD数据插值...")
    # 将AOD数据插值到因果效应数据的网格上
    aod_interp = aod_ds['aod550'].interp(
        latitude=causal_ds['lat'],
        longitude=causal_ds['lon'],
        method='nearest'  # 使用nearest方法更快
    )
    print("AOD数据插值完成")
    
    # 提取因果效应和显著性
    causal_effect = causal_ds['aod_to_tcslw_tau1']
    significance = causal_ds['aod_to_tcslw_sig_tau1']

    # --- 3. 基于AOD浓度创建掩码 ---
    # 确保所有数组都有相同的维度
    if len(aod_interp.dims) > 2:
        aod_interp = aod_interp.squeeze()
    if len(significance.dims) > 2:
        significance = significance.squeeze()

    significant_mask = significance == 1
    high_aod_mask = (aod_interp > AOD_THRESHOLD) & significant_mask
    low_aod_mask = (aod_interp <= AOD_THRESHOLD) & significant_mask

    print(f"高AOD区域网格点数 (>{AOD_THRESHOLD:.4f}): {high_aod_mask.sum().item()}")
    print(f"低AOD区域网格点数 (<={AOD_THRESHOLD:.4f}): {low_aod_mask.sum().item()}")

    # --- 4. 计算冰水比例 ---
    print("开始处理云水数据...")
    # 检查原始数据维度
    print(f"原始TCIW数据维度: {tciw_ds['tciw'].dims}")
    print(f"原始TCSLW数据维度: {tcslw_ds['tcslw'].dims}")
    print(f"因果效应数据维度: {causal_ds['lat'].dims}, {causal_ds['lon'].dims}")

    # 先计算时间平均，再插值
    print("计算时间平均...")
    tciw_mean = tciw_ds['tciw'].mean(dim='valid_time')
    tcslw_mean = tcslw_ds['tcslw'].mean(dim='valid_time')

    # 然后插值到因果效应数据的网格
    print("插值TCIW数据...")
    tciw_interp = tciw_mean.interp(
        latitude=causal_ds['lat'],
        longitude=causal_ds['lon'],
        method='nearest'  # 使用nearest方法更快
    )
    print("插值TCSLW数据...")
    tcslw_interp = tcslw_mean.interp(
        latitude=causal_ds['lat'],
        longitude=causal_ds['lon'],
        method='nearest'  # 使用nearest方法更快
    )
    print("云水数据插值完成")

    # 计算冰水比例
    denominator = tcslw_interp + tciw_interp
    print(f"分母形状: {denominator.shape}")
    print(f"分母中大于1e-9的点数: {(denominator > 1e-9).sum().item()}")

    # 检查是否有有效数据
    valid_denom = denominator > 1e-9
    if valid_denom.sum().item() > 0:
        print(f"分母统计: min={denominator.where(valid_denom).min().item():.2e}, max={denominator.where(valid_denom).max().item():.2e}")
    else:
        print("警告：所有分母值都太小（<=1e-9）")

    ice_ratio = xr.where(denominator > 1e-9, tciw_interp / denominator, np.nan)
    ice_ratio_mean = ice_ratio.clip(0, 1)  # 不需要再计算时间平均，因为已经计算过了

    # 检查冰水比例
    valid_ice_ratio = ~np.isnan(ice_ratio_mean)
    if valid_ice_ratio.sum().item() > 0:
        print(f"冰水比例统计: min={ice_ratio_mean.where(valid_ice_ratio).min().item():.4f}, max={ice_ratio_mean.where(valid_ice_ratio).max().item():.4f}")
    else:
        print("警告：所有冰水比例都是NaN")
    print(f"冰水比例中非NaN的点数: {valid_ice_ratio.sum().item()}")

    # 现在ice_ratio_mean已经在正确的网格上了
    ice_ratio_interp = ice_ratio_mean

    # --- 5. 提取不同AOD区域的数据 ---
    # 调试信息
    print(f"ice_ratio_interp shape: {ice_ratio_interp.shape}")
    print(f"high_aod_mask shape: {high_aod_mask.shape}")
    print(f"low_aod_mask shape: {low_aod_mask.shape}")

    # 将数据和掩码都展平为1D数组
    ice_ratio_flat = ice_ratio_interp.values.flatten()
    high_aod_mask_flat = high_aod_mask.values.flatten()
    low_aod_mask_flat = low_aod_mask.values.flatten()

    # 使用展平的掩码提取数据
    high_aod_data = ice_ratio_flat[high_aod_mask_flat]
    low_aod_data = ice_ratio_flat[low_aod_mask_flat]
    
    # 清理NaN值
    print(f"清理前 - 高AOD数据点: {high_aod_data.size}, 低AOD数据点: {low_aod_data.size}")
    print(f"高AOD数据中NaN的数量: {np.isnan(high_aod_data).sum()}")
    print(f"低AOD数据中NaN的数量: {np.isnan(low_aod_data).sum()}")

    high_aod_data = high_aod_data[~np.isnan(high_aod_data)]
    low_aod_data = low_aod_data[~np.isnan(low_aod_data)]

    if high_aod_data.size == 0 or low_aod_data.size == 0:
        print("警告：一个或两个AOD区域没有数据点。")
        print(f"高AOD区域有效数据点: {high_aod_data.size}")
        print(f"低AOD区域有效数据点: {low_aod_data.size}")
        return
    
    print(f"高AOD区域有效数据点: {high_aod_data.size}")
    print(f"低AOD区域有效数据点: {low_aod_data.size}")

    # --- 6. 绘制PDF对比图 ---
    sns.set_theme()
    high_aod_color = (231/255, 56/255, 69/255)    # 高AOD区域颜色
    low_aod_color = (69/255, 123/255, 157/255)    # 低AOD区域颜色
    high_aod_edge = (180/255, 40/255, 50/255)
    low_aod_edge = (40/255, 90/255, 120/255)
    
    fig, ax = plt.subplots(figsize=(12, 8), dpi=100)

    bins = np.linspace(0, 1, 51)

    # 绘制直方图
    ax.hist(low_aod_data, bins=bins, density=True, color=low_aod_color, alpha=0.4,
            edgecolor=low_aod_edge, label=f'Low AOD Region (≤{AOD_THRESHOLD:.4f}, n={low_aod_data.size})')
    ax.hist(high_aod_data, bins=bins, density=True, color=high_aod_color, alpha=0.4,
            edgecolor=high_aod_edge, label=f'High AOD Region (>{AOD_THRESHOLD:.4f}, n={high_aod_data.size})')

    # KDE曲线
    x_dense = np.linspace(0, 1, 200)
    
    if low_aod_data.size > 1:
        low_kde = gaussian_kde(low_aod_data)
        ax.plot(x_dense, low_kde(x_dense), color=low_aod_color, linewidth=2, 
                label='Low AOD PDF')
    
    if high_aod_data.size > 1:
        high_kde = gaussian_kde(high_aod_data)
        ax.plot(x_dense, high_kde(x_dense), color=high_aod_color, linewidth=2, 
                label='High AOD PDF')

    # 统计信息
    high_mean, high_median = np.mean(high_aod_data), np.median(high_aod_data)
    low_mean, low_median = np.mean(low_aod_data), np.median(low_aod_data)

    stats_text = (
        f"High AOD Region: Mean={high_mean:.4f}, Median={high_median:.4f}\n"
        f"Low AOD Region: Mean={low_mean:.4f}, Median={low_median:.4f}"
    )
    props = dict(boxstyle='round', facecolor='wheat', alpha=0.7)
    ax.text(0.98, 0.8, stats_text, transform=ax.transAxes, fontsize=11, 
            ha='right', va='center', bbox=props)

    # 中位数线
    ax.axvline(high_median, color=high_aod_color, linestyle='--', linewidth=1.5)
    ax.axvline(low_median, color=low_aod_color, linestyle='--', linewidth=1.5)

    ax.set_title(f'Distribution of Ice Water Ratio in High/Low AOD Regions (Threshold={AOD_THRESHOLD:.4f})', 
                fontsize=14, pad=20)
    ax.set_xlabel('Ice Water Ratio TCIW/(TCSLW+TCIW)', fontsize=12)
    ax.set_ylabel('Probability Density', fontsize=12)
    ax.legend(fontsize=10, loc='upper right')
    ax.set_xlim(0, 1)
    ax.set_ylim(bottom=0)
    ax.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig('aod_based_ice_ratio_pdf.png', dpi=300, bbox_inches='tight')
    plt.show()

if __name__ == "__main__":
    analyze_and_plot_aod_based_causal_impact() 
