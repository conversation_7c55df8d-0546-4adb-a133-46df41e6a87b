import xarray as xr
import numpy as np
import cartopy.crs as ccrs
import matplotlib.pyplot as plt

# 设置文件路径
data_dir = r'D:\dataset\supercooled'
tcslw_to_aod_file = f'{data_dir}/tcslw_to_aod_multi_tau_causal_effect.nc'

def load_results(file_path, tau):
    try:
        ds = xr.open_dataset(file_path)
        return (ds[f'tcslw_to_aod_tau{tau}'].values, 
                ds[f'tcslw_to_aod_sig_tau{tau}'].values,
                ds['lat'].values, 
                ds['lon'].values)
    except Exception as e:
        print(f"读取变量错误: {e}")
        return None, None, None, None

def plot_spatial(data, sig_data, lats, lons, title, save_name, tau):
    if data is None or sig_data is None:
        print(f"数据为空，跳过绘图: {title}")
        return
        
    plt.figure(figsize=(12, 8))
    ax = plt.axes(projection=ccrs.PlateCarree())
    ax.coastlines()
    
    im = ax.pcolormesh(lons, lats, data, cmap='RdBu_r',
                      transform=ccrs.PlateCarree(), vmin=-1, vmax=1)
    plt.colorbar(im, ax=ax, label='Causal Strength', orientation='horizontal', pad=0.05)
    
    # 网格线设置
    gl = ax.gridlines(crs=ccrs.PlateCarree(), draw_labels=True,
                     linewidth=1, color='gray', alpha=0.5, linestyle='--',
                     xlocs=range(-180, 181, 60),
                     ylocs=[-90, -60, -30, 0, 30, 60, 90])
    gl.top_labels = False
    gl.right_labels = False
    
    # 5度间隔显示显著点
    lat_grid = np.arange(0, len(lats), 5)
    lon_grid = np.arange(0, len(lons), 5)
    
    for lat_idx in lat_grid:
        for lon_idx in lon_grid:
            if sig_data[lat_idx, lon_idx]:
                ax.scatter(lons[lon_idx], lats[lat_idx],
                         color='black', s=3, transform=ccrs.PlateCarree())
    
    if tau == 1:
        ax.set_title(f'{title} (Lag {tau} month)')  # 单数形式
    else:
        ax.set_title(f'{title} (Lag {tau} months)')  # 复数形式
    plt.savefig(f'{data_dir}/{save_name}_tau{tau}.png', dpi=600, bbox_inches='tight')
    plt.show()

print("开始绘图...")

# 绘制有时间滞后结果
# 修改调用部分
for tau in [1, 2, 3]:
    data, sig_data, lats, lons = load_results(tcslw_to_aod_file, tau)
    if data is not None:
        plot_spatial(data, sig_data, lats, lons,
                   'Causal Effect of TCSLW on AOD550',
                   'tcslw_to_aod', tau)

print("绘图完成！")