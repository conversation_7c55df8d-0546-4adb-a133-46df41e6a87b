import xarray as xr
import matplotlib.pyplot as plt
import cartopy.crs as ccrs
from matplotlib.colors import LinearSegmentedColormap
from matplotlib.ticker import FuncFormatter
import matplotlib.gridspec as gridspec

# 加载数据
data_path = r'D:\dataset\supercooled\seasonavgslw.nc'
ds = xr.open_dataset(data_path)
seasonal_avg = ds['tcslw']
seasonal_avg.attrs = {}  # 清除数据属性

# 创建图形
plt.figure(figsize=(16, 12))
gs = gridspec.GridSpec(2, 2, width_ratios=[1, 1], height_ratios=[1, 1])

# 导入 cmocean 库
import cmocean

# 定义自定义颜色映射
cmap = cmocean.cm.deep  # 使用 cmocean 的 deep 颜色映射

# 定义等值线级别
levels = [0, 0.015, 0.03, 0.045, 0.06, 0.075, 0.09, 0.105, 0.12]

# 绘制每个季节的子图
for i, season in enumerate(['DJF', 'MAM', 'JJA', 'SON']):
    ax = plt.subplot(gs[i], projection=ccrs.PlateCarree())

    # 直接按顺序选择数据，因为valid_time维度已经是4个季节
    season_data = seasonal_avg.isel(valid_time=i)
    season_data.attrs = {}  # 清除数据属性

    season_data.plot.contourf(
        ax=ax,
        transform=ccrs.PlateCarree(),
        cmap=cmap,
        levels=levels,
        vmin=0,
        vmax=0.12,
        add_colorbar=False,
        add_labels=False  # 避免默认标题显示
    )

    # 地图元素
    ax.coastlines()
    # 直接设置固定标题
    titles = ['DJF Seasonal Average',
              'MAM Seasonal Average',
              'JJA Seasonal Average',
              'SON Seasonal Average']
    ax.set_title(titles[i], loc='center', fontsize=10)  # 将loc从'left'改为'center'
    
    # 网格线设置
    gl = ax.gridlines(crs=ccrs.PlateCarree(), draw_labels=True,
                      linewidth=1, color='gray', alpha=0.5, linestyle='--')
    gl.top_labels = False
    gl.right_labels = False

    # 只在底部和左侧子图显示坐标标签
    if i < 2:
        gl.bottom_labels = False
    if i % 2 == 1:
        gl.left_labels = False

# 定义自定义颜色映射
colors = ['white', 'green', 'yellow', 'red']
cmap = LinearSegmentedColormap.from_list('custom_cmap', colors)

# 添加共享的颜色条
cbar_ax = plt.gcf().add_axes([0.1875, 0.05, 0.525, 0.02])  
cbar = plt.colorbar(
    plt.cm.ScalarMappable(cmap=cmocean.cm.deep, norm=plt.Normalize(0, 0.12)),
    cax=cbar_ax,
    orientation='horizontal',
    label='TCSLW (kg/m²)',  # 添加单位
    ticks=levels,  # 设置等值线级别
    extend='max'  # 仅保留右侧箭头
)
cbar.set_ticks([0, 0.015, 0.03, 0.045, 0.06, 0.075, 0.09, 0.105, 0.12])

# 调整布局
plt.subplots_adjust(wspace=0.1, hspace=0.1)  # 将 hspace 从 0.2 调整为 0.1

# 输出图形
try:
    plt.show()
except Exception as e:
    print(f"图形显示错误: {e}")
    output_path = r"D:\dataset\program\season_avg_plot.png"
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    print(f"图形已保存至: {output_path}")
else:
    # 添加保存图形的else分支
    output_path = r"D:\dataset\program\season_avg_plot.png"
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    print(f"图形已保存至: {output_path}")

# 绘制纬向平均
ax2 = plt.subplot(gs[1])
# 获取地图中的纬度范围
lat_min, lat_max = monthly_avg['latitude'].min(), monthly_avg['latitude'].max()
zonalavg = monthly_avg.mean(dim='longitude')
zonalavg.plot(y='latitude', ax=ax2)
ax2.set_ylim(lat_min, lat_max)  # 显式设置y轴范围与地图一致

# 坐标轴格式化
ax2.yaxis.set_major_formatter(FuncFormatter(lat_format))
ax2.set_ylabel('Latitude')
ax2.yaxis.set_label_position("right")
ax2.yaxis.set_ticks(range(-60, 61, 30))  # 修改为不显示90度

# 子图2标题和样式
ax2.set_title('Zonal Mean of TCSLW')
ax2.set_ylabel('')
ax2.set_xlabel('')