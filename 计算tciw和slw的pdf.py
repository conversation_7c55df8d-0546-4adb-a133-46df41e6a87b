import netCDF4 as nc
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime

# 读取NC文件
file_path = r"D:\dataset\supercooled\tciw\tcw-tciw-79-24.nc"
ds = nc.Dataset(file_path)

# 提取变量 - 根据实际文件内容调整
tclw = ds.variables['tclw'][:]  # 使用tclw替代不存在的tcw变量
tciw = ds.variables['tciw'][:]  
valid_time = ds.variables['valid_time'][:]  

# 转换时间为datetime对象，并筛选2000-2010年
start_date = datetime(2000, 1, 1)
end_date = datetime(2005, 12, 31)
valid_dates = np.array([datetime.utcfromtimestamp(t) for t in valid_time])
mask_time = (valid_dates >= start_date) & (valid_dates <= end_date)

# 筛选对应时间的数据
tclw_filtered = tclw[mask_time, :, :]
tciw_filtered = tciw[mask_time, :, :]

# 计算冰晶比例（处理分母为0和NaN）
denominator = tclw_filtered + tciw_filtered
ice_fraction = np.divide(tciw_filtered, denominator, out=np.zeros_like(tciw_filtered), where=denominator!=0)
ice_fraction = np.ma.masked_invalid(ice_fraction)

# 定义过冷水阈值（0.03 kg m⁻²）
threshold = 0.03737
mask_low = tclw_filtered < threshold
mask_high = tclw_filtered > threshold

# 提取两组的冰晶比例数据
ice_low = ice_fraction[mask_low].flatten()
ice_high = ice_fraction[mask_high].flatten()

# 绘制PDF图
plt.figure(figsize=(10, 6))
plt.hist(ice_low, bins=50, density=True, alpha=0.5, label='TCLW < 0.03 kg m⁻²')
plt.hist(ice_high, bins=50, density=True, alpha=0.5, label='TCLW > 0.03 kg m⁻²')
plt.xlabel('Ice Fraction (Ice Water / (Liquid Water + Ice Water))')
plt.ylabel('Probability Density')
plt.title('PDF of Ice Fraction under Different Supercooled Water Conditions (2000-2010)')
plt.legend()
plt.grid(True)
plt.show()

# 关闭文件
ds.close()