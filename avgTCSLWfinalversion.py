import xarray as xr
import matplotlib.pyplot as plt
import cartopy.crs as ccrs
from matplotlib.colors import LinearSegmentedColormap
from matplotlib.ticker import FormatStrFormatter, FuncFormatter
import matplotlib.gridspec as gridspec
# 导入 cmocean 库
import cmocean

# 加载数据
data_path = r'D:\dataset\supercooled\monthly_avg_tcslw.nc'
ds = xr.open_dataset(data_path)
monthly_avg = ds['tcslw']

plt.figure(figsize=(12, 6))
gs = gridspec.GridSpec(1, 2, width_ratios=[2, 1])  # 调整子图宽度比例
# 使用 cmocean 的 deep 颜色映射
cmap = cmocean.cm.deep

# 定义等值线级别
levels = [0, 0.015, 0.03, 0.045, 0.06, 0.075, 0.09, 0.105, 0.12]

# 绘制总月平均（使用cartopy绘制二维地图）
ax1 = plt.subplot(gs[0], projection=ccrs.PlateCarree())
monthly_avg.plot.contourf(ax=ax1, transform=ccrs.PlateCarree(), cmap=cmap,
                          levels=levels,  # 设置等值线级别
                          vmin=0, vmax=0.12,
                          cbar_kwargs={
                              'label': 'kg/m²',
                              'orientation': 'horizontal',
                              'ticks': levels,
                              'extend': 'neither'  # 新增这行，去掉图例箭头
                          })

# 地图元素
ax1.coastlines()

# 网格线设置
gl = ax1.gridlines(crs=ccrs.PlateCarree(), draw_labels=True,
                   linewidth=1, color='gray', alpha=0.5, linestyle='--',
                   xlocs=range(-180, 181, 60),  # 经度每30度显示一个刻度
                   ylocs=[-90, -60, -30, 0, 30, 60, 90])  # 确保包含90度
gl.top_labels = False
gl.right_labels = False

# 标题和标签
ax1.set_title('Monthly Average TCSLW', pad=10)
ax1.set_ylabel('Latitude')
ax1.set_xlabel('Longitude')

# 定义一个自定义的刻度格式化函数
def lat_format(x, pos):
    if x >= 0:
        return f"{int(x)}°N"
    else:
        return f"{int(-x)}°S"

# 绘制纬向平均
ax2 = plt.subplot(gs[1])
# 获取地图中的纬度范围
lat_min, lat_max = monthly_avg['latitude'].min(), monthly_avg['latitude'].max()
monthly_avg.mean(dim='longitude').plot(y='latitude', ax=ax2)
ax2.set_ylim(lat_min, lat_max)  # 显式设置y轴范围与地图一致

# 坐标轴格式化
ax2.yaxis.set_major_formatter(FuncFormatter(lat_format))
ax2.set_ylabel('Latitude')
ax2.yaxis.set_label_position("right")
ax2.yaxis.set_ticks(range(-60, 61, 30))  # 修改为不显示90度

# 子图2标题和样式
ax2.set_title('Zonal Mean of TCSLW')
ax2.set_ylabel('')
ax2.set_xlabel('')

# 调整子图间距
plt.subplots_adjust(wspace=0.3)

# 输出图形
try:
    plt.tight_layout()
    plt.show()
except Exception as e:
    print(f"图形显示错误: {e}")
    output_path = r"D:\dataset\program\plot_output.png"
    plt.savefig(output_path, dpi=600, bbox_inches='tight')
    print(f"图形已保存至: {output_path}")