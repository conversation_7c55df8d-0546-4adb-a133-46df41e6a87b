import xarray as xr
import numpy as np
import pandas as pd
import cartopy.crs as ccrs
import matplotlib.pyplot as plt
from tigramite import data_processing as pp
from tigramite.pcmci import PCMCI
from tigramite.independence_tests.parcorr import <PERSON>r<PERSON><PERSON><PERSON>
from joblib import Parallel, delayed
from tqdm import tqdm

# 设置文件路径和参数
data_dir = r'D:\dataset\supercooled'
water_file = 'reBc_bcaod550.nc'
aod_file = '03-23supercooledwater.nc'

def load_data(var_file, var_name):
    ds = xr.open_dataset(f'{data_dir}/{var_file}')
    return ds[var_name]

water_data = load_data(water_file, 'bcaod550')
aod_data = load_data(aod_file, 'tcslw')

time_dim = [dim for dim in water_data.dims if 'time' in dim.lower()][0]
lats = water_data.lat.values
lons = water_data.lon.values

bcaod550_to_tcslw = np.zeros((len(lats), len(lons)))
tcslw_to_bcaod550 = np.zeros((len(lats), len(lons)))
bcaod550_to_tcslw_sig = np.zeros((len(lats), len(lons)))
tcslw_to_bcaod550_sig = np.zeros((len(lats), len(lons)))

parcorr = ParCorr(significance='analytic')
tau_max = 6
pc_alpha = 0.05

def process_point(i, j, lat, lon):
    try:
        water_ts = water_data.sel(lat=lat, lon=lon).values
        aod_ts = aod_data.sel(lat=lat, lon=lon).values
        
        df = pd.DataFrame({
            'bcaod550': water_ts,
            'tcslw': aod_ts
        }, index=pd.to_datetime(water_data[time_dim].values))
        
        if df.isnull().any().any():
            df = df.interpolate().dropna()
            
        data = df.values
        dataframe = pp.DataFrame(data, datatime=df.index, var_names=['bcaod550', 'tcslw'])
        
        pcmci = PCMCI(
            dataframe=dataframe,
            cond_ind_test=parcorr,
            verbosity=0
        )
        
        results = pcmci.run_pcmci(tau_max=tau_max, pc_alpha=pc_alpha)
        sig_matrix = results['p_matrix'] < pc_alpha
        
        return i, j, results['val_matrix'][0, 1, 1], results['val_matrix'][1, 0, 1], \
               sig_matrix[0, 1, 1], sig_matrix[1, 0, 1]
        
    except Exception as e:
        print(f"Error at lat={lat}, lon={lon}: {str(e)}")
        return i, j, np.nan, np.nan, False, False

print("开始并行计算...")
results = Parallel(n_jobs=-1)(delayed(process_point)(i, j, lat, lon)
                             for i, lat in enumerate(tqdm(lats, desc="纬度进度"))
                             for j, lon in enumerate(lons))

# 过滤掉None值
valid_results = [res for res in results if res is not None]

for i, j, bcaod550_tcslw, tcslw_bcaod550, bcaod550_tcslw_sig_flag, tcslw_bcaod550_sig_flag in valid_results:
    bcaod550_to_tcslw[i, j] = bcaod550_tcslw
    tcslw_to_bcaod550[i, j] = tcslw_bcaod550
    bcaod550_to_tcslw_sig[i, j] = bcaod550_tcslw_sig_flag
    tcslw_to_bcaod550_sig[i, j] = tcslw_bcaod550_sig_flag

def save_to_nc(data, sig_data, var_name, description):
    ds = xr.Dataset(
        {
            var_name: (["lat", "lon"], data),
            f"{var_name}_sig": (["lat", "lon"], sig_data)
        },
        coords={
            "lat": lats,
            "lon": lons
        }
    )
    ds.to_netcdf(f'{data_dir}/{var_name}_causal_effect_with_sig.nc')

save_to_nc(bcaod550_to_tcslw, bcaod550_to_tcslw_sig, 'bcaod550_to_tcslw', 'Causal effect of BCAOD550 on TCSLW')
save_to_nc(tcslw_to_bcaod550, tcslw_to_bcaod550_sig, 'tcslw_to_bcaod550', 'Causal effect of TCSLW on BCAOD550')

def plot_spatial(data, sig_data, title, save_name):
    plt.figure(figsize=(12, 8))
    ax = plt.axes(projection=ccrs.PlateCarree())
    ax.coastlines()
    
    im = ax.pcolormesh(lons, lats, data, cmap='RdBu_r', 
                      transform=ccrs.PlateCarree(), vmin=-1, vmax=1)
    plt.colorbar(im, ax=ax, orientation='horizontal', pad=0.05)
    
    lat_grid = np.arange(0, len(lats), 4)
    lon_grid = np.arange(0, len(lons), 4)
    
    for lat_idx in lat_grid:
        for lon_idx in lon_grid:
            if sig_data[lat_idx, lon_idx]:
                ax.scatter(lons[lon_idx], lats[lat_idx], 
                           color='black', s=3, transform=ccrs.PlateCarree())
    
    ax.set_title(title)  # 修复：添加右括号
    plt.savefig(f'{data_dir}/{save_name}.png', dpi=300, bbox_inches='tight')
    plt.show()

print("开始绘图...")
plot_spatial(bcaod550_to_tcslw, bcaod550_to_tcslw_sig, 
            'Causal Effect of BCAOD550 on TCSLW', 'bcaod550_to_tcslw')
plot_spatial(tcslw_to_bcaod550, tcslw_to_bcaod550_sig, 
            'Causal Effect of TCSLW on BCAOD550', 'tcslw_to_bcaod550')
print("处理完成！")