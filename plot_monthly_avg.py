import xarray as xr
import matplotlib.pyplot as plt
import cartopy.crs as ccrs
import cartopy.feature as cfeature

# 加载数据
data_path = r'D:\dataset\supercooled\monthly_avg_tcslw.nc'
ds = xr.open_dataset(data_path)
monthly_avg = ds['bcaod550']  # 假设变量名是 'bcaod550'

# 绘图
plt.figure(figsize=(12, 6))

# 绘制总月平均（使用cartopy绘制二维地图）
ax1 = plt.subplot(1, 2, 1, projection=ccrs.PlateCarree())
monthly_avg.plot(ax=ax1, transform=ccrs.PlateCarree(), cmap='Blues', 
                 cbar_kwargs={'label': 'Total column supercooled liquid water', 'orientation': 'horizontal'})
ax1.coastlines()
ax1.add_feature(cfeature.BORDERS, linestyle=':')
ax1.set_title('Monthly average Total column supercooled liquid water')
ax1.set_ylabel('Latitude')  # 标注纬度（英文）
ax1.set_xlabel('Longitude')  # 标注经度（英文）

# 绘制纬向平均（横坐标改为纵坐标，长度变为原来的四分之一）
ax2 = plt.subplot(1, 2, 2, sharey=ax1)  # 共享y轴
monthly_avg.mean(dim='lon').plot(y='lat', ax=ax2)  # 计算纬向平均并绘图
ax2.set_title('Zonal Mean of Total Column SupercooledLiquid Water')
ax2.set_ylabel('')  # 不显示ylabel，与左边图共享
ax2.set_xlabel('')  # 不显示xlabel

# 调整右边图的宽度
plt.subplots_adjust(right=0.75)  # 右边图宽度变为原来的四分之一

plt.tight_layout()
plt.show()