import xarray as xr
import numpy as np
import cartopy.crs as ccrs
import matplotlib.pyplot as plt

# 设置文件路径（读取路径保持不变）
data_dir = r'D:\dataset\supercooled\bc'  # 读取路径
save_dir = r'D:\dataset\supercooled\bc\BC-hydrophobic'  # 新增保存路径

tcslw_to_aod_file = f'{data_dir}/tcslw_to_aod550_multi_tau.nc'  # 读取路径使用原bc目录
aod_to_tcslw_file = f'{data_dir}/aod550_to_tcslw_multi_tau.nc'  # 读取路径使用原bc目录

# 读取结果文件
def load_results(file_path, var_name, tau=None):
    try:
        ds = xr.open_dataset(file_path)
        if tau is None or tau == 0:
            return ds[f'{var_name}_tau1'].values, ds[f'{var_name}_tau1_sig'].values, ds['lat'].values, ds['lon'].values
        else:
            return ds[f'{var_name}_tau{tau}'].values, ds[f'{var_name}_tau{tau}_sig'].values, ds['lat'].values, ds['lon'].values
    except Exception as e:
        print(f"读取变量错误: {e}")
        return None, None, None, None

# 绘制空间分布图
def plot_spatial(data, sig_data, title, save_name, tau=None):
    if data is None or sig_data is None:
        print(f"数据为空，跳过绘图: {title}")
        return
    
    plt.figure(figsize=(12, 8))
    ax = plt.axes(projection=ccrs.PlateCarree())
    ax.coastlines()
    
    im = ax.pcolormesh(lons, lats, data, cmap='RdBu_r',
                      transform=ccrs.PlateCarree(), vmin=-1, vmax=1)
    plt.colorbar(im, ax=ax, label='Causal Strength', orientation='horizontal', pad=0.05)
    
    gl = ax.gridlines(crs=ccrs.PlateCarree(), draw_labels=True,
                     linewidth=1, color='gray', alpha=0.5, linestyle='--',
                     xlocs=range(-180, 181, 60),
                     ylocs=[-90, -60, -30, 0, 30, 60, 90])
    gl.top_labels = False
    gl.right_labels = False
    
    lat_grid = np.arange(0, len(lats), 5)
    lon_grid = np.arange(0, len(lons), 5)
    
    for lat_idx in lat_grid:
        for lon_idx in lon_grid:
            if sig_data[lat_idx, lon_idx]:
                ax.scatter(lons[lon_idx], lats[lat_idx],
                           color='black', s=3, transform=ccrs.PlateCarree())
    
    if tau is None or tau == 0:
        ax.set_title(f'{title} (Instantaneous)')
        plt.savefig(f'{save_dir}/{save_name}_instant.png', dpi=600, bbox_inches='tight')  # 保存到BC-hydrophobic目录
    elif tau == 1:
        ax.set_title(f'{title} (Lag {tau} month)')
    else:
        ax.set_title(f'{title} (Lag {tau} months)')
    
    plt.savefig(f'{save_dir}/{save_name}_tau{tau}.png', dpi=600, bbox_inches='tight')  # 保存到BC-hydrophobic目录
    plt.close()  # 关闭图形，不显示

print("开始绘图...")

# 绘制无时间滞后结果
tcslw_to_aod_inst, tcslw_sig_inst, lats, lons = load_results(tcslw_to_aod_file, 'tcslw_to_aod550')
if tcslw_to_aod_inst is not None:
    plot_spatial(tcslw_to_aod_inst, tcslw_sig_inst,
                'Causal Effect of TCSLW on BC-hydrophobic', 'tcslw_to_aod550')

aod_to_tcslw_inst, aod_sig_inst, _, _ = load_results(aod_to_tcslw_file, 'aod550_to_tcslw')
if aod_to_tcslw_inst is not None:
    plot_spatial(aod_to_tcslw_inst, aod_sig_inst,
                'Causal Effect of BC-hydrophobic on TCSLW', 'aod550_to_tcslw')

# 绘制无时间滞后结果
# 由于文件中没有无时间滞后的变量，跳过无时间滞后的绘图

# 绘制有时间滞后结果
for tau in [1, 2, 3]:
    tcslw_to_aod, tcslw_sig, lats, lons = load_results(tcslw_to_aod_file, 'tcslw_to_aod550', tau)
    plot_spatial(tcslw_to_aod, tcslw_sig,
                'Causal Effect of TCSLW on BC-hydrophobic', 'tcslw_to_aod550', tau)
    
    aod_to_tcslw, aod_sig, _, _ = load_results(aod_to_tcslw_file, 'aod550_to_tcslw', tau)
    plot_spatial(aod_to_tcslw, aod_sig,
                'Causal Effect of BC-hydrophobic on TCSLW', 'aod550_to_tcslw', tau)

print("绘图完成！")
