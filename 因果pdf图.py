import xarray as xr
import numpy as np
import matplotlib.pyplot as plt
from scipy.stats import gaussian_kde
import matplotlib as mpl
import seaborn as sns

def analyze_and_plot_causal_impact():
    """
    分析AOD对TCSLW的因果效应与云的冰相态的关系：
    1. 从aod_to_tcslw_multi_tau_causal_effect.nc中提取：
       - aod_to_tcslw_tau1：AOD对TCSLW的因果效应（时间滞后1）
       - aod_to_tcslw_sig_tau1：显著性检验结果
    2. 识别显著的正负因果区域（p < 0.05）
    3. 从tcw-tciw-79-24.nc计算 tciw/(tcw+tciw) 的时间平均值
    4. 绘制两类区域的PDF对比图
    """
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']  # 优先使用的中文字体列表
    plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题
    
    # --- 1. 加载数据 ---
    try:
        # 加载因果效应数据
        causal_ds = xr.open_dataset('D:/dataset/supercooled/aod_to_tcslw_multi_tau_causal_effect.nc')
        # 加载云水数据
        cloud_ds = xr.open_dataset('D:/dataset/supercooled/tciw/tcw-tciw-79-24.nc')
        print("数据文件加载成功。")
        
        # 打印数据集结构信息
        print("\n因果效应数据集结构:")
        print(causal_ds)
        print("\n云水数据集结构:")
        print(cloud_ds)
        
        # 获取维度名称
        causal_dims = list(causal_ds.dims.keys())
        cloud_dims = list(cloud_ds.dims.keys())
        print("\n维度名称:")
        print(f"因果效应数据维度: {causal_dims}")
        print(f"云水数据维度: {cloud_dims}")
        
    except FileNotFoundError as e:
        print(f"错误: {e}")
        print("请确保指定的文件路径正确。")
        return

    # --- 2. 提取变量并创建掩码 ---
    # 提取AOD对TCSLW的因果效应（时间滞后1）
    causal_effect = causal_ds['aod_to_tcslw_tau1']
    # 提取显著性检验结果
    significance = causal_ds['aod_to_tcslw_sig_tau1']

    # 创建显著区域的掩码（p < 0.05）
    significant_mask = significance == 1  # True表示显著
    print(f"识别出{significant_mask.sum().item()}个显著网格点。")

    # 创建显著的正负因果效应区域掩码
    positive_mask = (causal_effect > 0) & significant_mask
    negative_mask = (causal_effect < 0) & significant_mask

    print(f"正因果区域网格点数: {positive_mask.sum().item()}")
    print(f"负因果区域网格点数: {negative_mask.sum().item()}")

    # --- 3. 计算冰水比例并进行网格插值 ---
    print("计算冰水比例 TCIW/(TCW+TCIW)...")
    
    # 首先计算每个时间点的冰水比例
    tciw = cloud_ds['tciw']
    tclw = cloud_ds['tclw']
    tcw = tclw + tciw
    
    # 计算冰水比例：TCIW/(TCLW+TCIW)
    denominator = tclw + tciw
    ice_ratio = xr.where(denominator > 1e-9, tciw / denominator, np.nan)
    
    # 对时间维度取平均
    ice_ratio_mean = ice_ratio.mean(dim='valid_time')
    ice_ratio_mean = ice_ratio_mean.clip(0, 1)  # 确保比例在[0,1]范围内

    # 将ice_ratio_mean插值到因果效应数据的网格上
    ice_ratio_interp = ice_ratio_mean.interp(
        latitude=causal_ds['lat'],
        longitude=causal_ds['lon'],
        method='linear'
    )

    print("数据插值完成。")

    # --- 4. 提取不同因果区域的数据 ---
    print("提取正负因果区域的数据...")
    
    # 使用掩码提取数据
    pos_data = ice_ratio_interp.values[positive_mask.values]
    neg_data = ice_ratio_interp.values[negative_mask.values]
    
    # 清理数据，移除NaN值
    pos_data = pos_data[~np.isnan(pos_data)]
    neg_data = neg_data[~np.isnan(neg_data)]
    
    if pos_data.size == 0 or neg_data.size == 0:
        print("警告：掩码后一个或两个类别没有数据点。")
        print(f"正因果区域的数据点数: {pos_data.size}")
        print(f"负因果区域的数据点数: {neg_data.size}")
        return
    
    print(f"在正因果区域找到{pos_data.size}个有效数据点。")
    print(f"在负因果区域找到{neg_data.size}个有效数据点。")

    # --- 5. 绘制PDF对比图 ---
    print("生成PDF图...")
    sns.set_theme()  # 使用新版本的seaborn样式
    # 定义自定义颜色（RGB值归一化到[0,1]）
    neg_color = (231/255, 56/255, 69/255)    # 负因果区域颜色 r231g056b069
    pos_color = (69/255, 123/255, 157/255)   # 正因果区域颜色 r069g123b157
    neg_edge = (180/255, 40/255, 50/255)     # 负区域边缘颜色（稍暗）
    pos_edge = (40/255, 90/255, 120/255)     # 正区域边缘颜色（稍暗）
    fig, ax = plt.subplots(figsize=(12, 8), dpi=100)

    # 定义直方图的区间
    bins = np.linspace(0, 1, 51)

    # 绘制直方图
    ax.hist(neg_data, bins=bins, density=True, color=neg_color, alpha=0.4,
            edgecolor=neg_edge, label=f'Negative Causal Region (n={neg_data.size})')
    ax.hist(pos_data, bins=bins, density=True, color=pos_color, alpha=0.4,
            edgecolor=pos_edge, label=f'Positive Causal Region (n={pos_data.size})')

    # 计算并绘制核密度估计(KDE)曲线
    x_dense = np.linspace(0, 1, 200)
    
    if pos_data.size > 1:  # KDE需要至少2个点
        pos_kde = gaussian_kde(pos_data)
        ax.plot(x_dense, pos_kde(x_dense), color=pos_color, linewidth=2, 
                label='Positive Region PDF')
    
    if neg_data.size > 1:
        neg_kde = gaussian_kde(neg_data)
        ax.plot(x_dense, neg_kde(x_dense), color=neg_color, linewidth=2, 
                label='Negative Region PDF')

    # --- 6. 添加统计信息和图表修饰 ---
    pos_mean, pos_median = np.mean(pos_data), np.median(pos_data)
    neg_mean, neg_median = np.mean(neg_data), np.median(neg_data)

    stats_text = (
        f"Positive Region: Mean={pos_mean:.4f}, Median={pos_median:.4f}\n"
        f"Negative Region: Mean={neg_mean:.4f}, Median={neg_median:.4f}"
    )
    props = dict(boxstyle='round', facecolor='wheat', alpha=0.7)
    # 将统计信息文本放在右边稍靠上位置
    ax.text(
        0.98, 0.8, stats_text,   # y从0.5改为0.65，稍微靠上
        transform=ax.transAxes, fontsize=11, ha='right', va='center',
        bbox=props
    )

    # 添加中位数的垂直线，并将label分别放在右上和右下，避免重叠
    ax.axvline(pos_median, color=pos_color, linestyle='--', linewidth=1.5)
    ax.axvline(neg_median, color=neg_color, linestyle='--', linewidth=1.5)


    ax.set_title('Distribution of Ice Water Ratio in AOD-TCSLW Causal Effect Regions', fontsize=14, pad=20)
    ax.set_xlabel('Ice Water Ratio TCIW/(TCLW+TCIW)', fontsize=12)
    ax.set_ylabel('Probability Density', fontsize=12)
    ax.legend(fontsize=10, loc='upper right')
    ax.set_xlim(0, 1)
    ax.set_ylim(bottom=0)
    ax.grid(True, alpha=0.3)

    plt.tight_layout()
    
    # 保存图片时也使用高DPI
    plt.savefig('ice_ratio_pdf.png', dpi=300, bbox_inches='tight')
    plt.show()

# --- 主执行块 ---
if __name__ == "__main__":
    analyze_and_plot_causal_impact() 