# 在文件顶部添加导入
import matplotlib.gridspec as gridspec

import xarray as xr
import matplotlib.pyplot as plt
import cartopy.crs as ccrs
import cmocean
import numpy as np
from matplotlib.ticker import MultipleLocator
import matplotlib.gridspec as gridspec
import pandas as pd

# 加载AOD数据
data_path = r'D:\dataset\supercooled\03-23aod.nc'
ds = xr.open_dataset(data_path)
monthly_avg = ds['aod550']

# 处理时间维度
times = pd.to_datetime(monthly_avg['valid_time'].values, unit='s')
monthly_avg['time'] = ('valid_time', times)
monthly_avg = monthly_avg.swap_dims({'valid_time': 'time'})

# 创建图形
plt.figure(figsize=(16, 12))
gs = gridspec.GridSpec(1, 2, width_ratios=[1, 0.3])

# 主图 - 全球AOD分布
ax1 = plt.subplot(gs[0], projection=ccrs.PlateCarree())

# 定义颜色映射和级别
cmap = cmocean.cm.matter
levels = np.linspace(0, 1.0, 11)  # 0到1.0，间隔0.1

monthly_avg.mean(dim='time').plot.contourf(
    ax=ax1,
    transform=ccrs.PlateCarree(),
    cmap=cmap,
    levels=levels,
    add_colorbar=False
)

# 地图元素
ax1.coastlines()
ax1.set_title('2003-2023 Monthly Mean AOD at 550nm', fontsize=12)

# 网格线设置
gl = ax1.gridlines(crs=ccrs.PlateCarree(), draw_labels=True,
                  linewidth=1, color='gray', alpha=0.5, linestyle='--')
gl.top_labels = False
gl.right_labels = False

# 添加颜色条
cbar_ax = plt.gcf().add_axes([0.25, 0.1, 0.5, 0.02])
cbar = plt.colorbar(
    plt.cm.ScalarMappable(cmap=cmap, norm=plt.Normalize(0, 1.0)),
    cax=cbar_ax,
    orientation='horizontal',
    label='AOD at 550nm',
    ticks=levels
)

# 右侧子图 - 纬向平均
ax2 = plt.subplot(gs[1])
zonalavg = monthly_avg.mean(dim=['time', 'lon'])
zonalavg.plot(y='lat', ax=ax2, color='red', linewidth=2)
ax2.set_ylim(-90, 90)
ax2.set_title('Zonal Mean AOD')
ax2.set_ylabel('Latitude')
ax2.yaxis.set_label_position("right")
ax2.yaxis.set_ticks(range(-90, 91, 30))
ax2.grid(True, linestyle='--', alpha=0.5)

# 调整布局
plt.subplots_adjust(wspace=0.3)

# 显示图形
plt.show()

# 保存图形
output_path = r"D:\dataset\program\aod_monthly_avg_plot.png"
plt.savefig(output_path, dpi=300, bbox_inches='tight')
print(f"图形已保存至: {output_path}")
plt.close()