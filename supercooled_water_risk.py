import numpy as np
import pandas as pd
import xarray as xr
from sklearn.preprocessing import StandardScaler
from sklearn.neighbors import KNeighborsClassifier
import matplotlib.pyplot as plt
import cartopy.crs as ccrs
import cartopy.feature as cfeature

def load_and_preprocess_data(file_path):
    """
    Load and preprocess the supercooled water data
    """
    # Load data using xarray
    ds = xr.open_dataset(file_path)
    
    # Extract supercooled water content and coordinates
    slw = ds['slw'].mean(dim='time')  # Assuming 'slw' is the variable name
    lat = ds.lat
    lon = ds.lon
    
    return slw, lat, lon

def create_feature_matrix(slw, lat, lon):
    """
    Create feature matrix for KNN classification
    """
    # Create meshgrid of coordinates
    LON, LAT = np.meshgrid(lon, lat)
    
    # Stack the features
    features = np.column_stack([
        LON.flatten(),
        LAT.flatten(),
        slw.values.flatten()
    ])
    
    # Remove NaN values
    valid_mask = ~np.isnan(features).any(axis=1)
    features = features[valid_mask]
    
    return features, valid_mask

def classify_risk_zones(features, n_classes=4, n_neighbors=5):
    """
    Classify risk zones using KNN
    """
    # Standardize features
    scaler = StandardScaler()
    features_scaled = scaler.fit_transform(features)
    
    # Define natural breaks for risk levels based on SLW content
    slw_values = features[:, 2]
    risk_levels = pd.qcut(slw_values, n_classes, labels=False)
    
    # Train KNN classifier
    knn = KNeighborsClassifier(n_neighbors=n_neighbors)
    knn.fit(features_scaled, risk_levels)
    
    # Predict risk levels
    risk_predictions = knn.predict(features_scaled)
    
    return risk_predictions, scaler, knn

def plot_risk_map(lat, lon, risk_levels, valid_mask):
    """
    Plot global risk zone map
    """
    # Create full grid of risk levels
    full_grid = np.full((len(lat), len(lon)), np.nan)
    full_grid.flat[valid_mask] = risk_levels
    
    # Create figure with map projection
    fig = plt.figure(figsize=(15, 10))
    ax = plt.axes(projection=ccrs.PlateCarree())
    
    # Add map features
    ax.add_feature(cfeature.COASTLINE)
    ax.add_feature(cfeature.BORDERS)
    
    # Plot risk levels
    risk_colors = ['green', 'yellow', 'orange', 'red']
    risk_labels = ['Low', 'Moderate', 'High', 'Severe']
    
    im = ax.pcolormesh(lon, lat, full_grid, 
                      transform=ccrs.PlateCarree(),
                      cmap=plt.cm.get_cmap('RdYlGn_r', 4))
    
    # Add colorbar
    cbar = plt.colorbar(im)
    cbar.set_ticks([0.5, 1.5, 2.5, 3.5])
    cbar.set_ticklabels(risk_labels)
    
    plt.title('Global Supercooled Water Risk Zones')
    plt.savefig('supercooled_water_risk_map.png', dpi=300, bbox_inches='tight')
    plt.close()

def main():
    # Load and process data
    file_path = 'path_to_your_data.nc'  # Update with your data file path
    slw, lat, lon = load_and_preprocess_data(file_path)
    
    # Create feature matrix
    features, valid_mask = create_feature_matrix(slw, lat, lon)
    
    # Classify risk zones
    risk_levels, scaler, knn = classify_risk_zones(features)
    
    # Plot results
    plot_risk_map(lat, lon, risk_levels, valid_mask)
    
    # Save model and scaler for future use
    import joblib
    joblib.dump(knn, 'slw_risk_knn_model.joblib')
    joblib.dump(scaler, 'slw_risk_scaler.joblib')

if __name__ == '__main__':
    main() 