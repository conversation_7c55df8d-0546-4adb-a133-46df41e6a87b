# -*- coding: utf-8 -*-
"""
Created on Fri Mar 21 08:54:02 2025

@author: willz
"""

import xarray as xr
import numpy as np
import pandas as pd
import cartopy.crs as ccrs
import matplotlib.pyplot as plt
from tigramite import data_processing as pp
from tigramite import plotting as tp
from tigramite.pcmci import PCMCI
from tigramite.independence_tests.parcorr import ParCorr
from joblib import Parallel, delayed
from tqdm import tqdm
from scipy import stats

# 设置文件路径和参数
data_dir = r'D:\dataset\supercooled'
water_file = '03-23supercooledwater.nc'
aod_file = '03-23aod.nc'

# 加载数据
def load_data(var_file, var_name):
    ds = xr.open_dataset(f'{data_dir}/{var_file}')
    return ds[var_name]

water_data = load_data(water_file, 'tcslw')
aod_data = load_data(aod_file, 'aod550')

# 获取时间维度名称
time_dim = [dim for dim in water_data.dims if 'time' in dim.lower()][0]

# 获取全球格点信息
lats = water_data.lat.values
lons = water_data.lon.values

# 创建空数组存储结果
# 修改结果数组结构以存储不同时间滞后的结果
tcslw_to_aod = np.zeros((3, len(lats), len(lons)))  # 3个时间滞后层
aod_to_tcslw = np.zeros((3, len(lats), len(lons))) 
tcslw_to_aod_sig = np.zeros((3, len(lats), len(lons))) 
aod_to_tcslw_sig = np.zeros((3, len(lats), len(lons)))

# PCMCI参数设置
parcorr = ParCorr(significance='analytic')
tau_max = 3  # 设置为3，表示考虑最多3个月的时间滞后
pc_alpha = 0.05  # 显著性水平

# 格点处理函数
def process_point(i, j, lat, lon):
    try:
        # 提取逐月时间序列
        water_ts = water_data.sel(lat=lat, lon=lon).values
        aod_ts = aod_data.sel(lat=lat, lon=lon).values
        
        # 转换为DataFrame
        df = pd.DataFrame({
            'tcslw': water_ts,
            'aod550': aod_ts
        }, index=pd.to_datetime(water_data[time_dim].values))
        
        # 处理缺失值
        if df.isnull().any().any():
            df = df.interpolate().dropna()
            
        # 使用tigramite计算因果关系
        dataframe = pp.DataFrame(df.values, datatime=df.index, var_names=['tcslw', 'aod550'])
        pcmci = PCMCI(
            dataframe=dataframe,
            cond_ind_test=parcorr,
            verbosity=0
        )
        
        # 运行PCMCI分析
        results = pcmci.run_pcmci(tau_max=tau_max, pc_alpha=pc_alpha)
        
        # 获取因果关系值和p值
        val_matrix = results['val_matrix']
        p_matrix = results['p_matrix']
        
        # 计算不同时间滞后的因果关系
        # 1个月滞后
        tcslw_aod_1 = val_matrix[0, 1, 1]  # tcslw -> aod (tau=1)
        aod_tcslw_1 = val_matrix[1, 0, 1]  # aod -> tcslw (tau=1)
        # 2个月滞后
        tcslw_aod_2 = val_matrix[0, 1, 2]  # tcslw -> aod (tau=2)
        aod_tcslw_2 = val_matrix[1, 0, 2]  # aod -> tcslw (tau=2)
        # 3个月滞后
        tcslw_aod_3 = val_matrix[0, 1, 3]  # tcslw -> aod (tau=3)
        aod_tcslw_3 = val_matrix[1, 0, 3]  # aod -> tcslw (tau=3)
        
        # 计算平均因果关系
        tcslw_aod = np.mean([tcslw_aod_1, tcslw_aod_2, tcslw_aod_3])
        aod_tcslw = np.mean([aod_tcslw_1, aod_tcslw_2, aod_tcslw_3])
        
        # 修改显著性检验处理方式
        # 不再计算p值平均值，而是分别返回各时间滞后的p值
        return i, j, \
               [tcslw_aod_1, tcslw_aod_2, tcslw_aod_3], \
               [aod_tcslw_1, aod_tcslw_2, aod_tcslw_3], \
               [p_matrix[0,1,1], p_matrix[0,1,2], p_matrix[0,1,3]], \
               [p_matrix[1,0,1], p_matrix[1,0,2], p_matrix[1,0,3]]
               
    except Exception as e:
        print(f"Error at lat={lat}, lon={lon}: {str(e)}")
        return i, j, [np.nan]*3, [np.nan]*3, [1.0]*3, [1.0]*3

# 修改结果填充部分
# 修改并行计算部分
print("开始并行计算...")
results_list = Parallel(n_jobs=-1)(delayed(process_point)(i, j, lat, lon)
                                 for i, lat in enumerate(tqdm(lats, desc="纬度进度"))
                                 for j, lon in enumerate(lons))

# 将结果填充到数组中
for result in results_list:
    i, j, tcslw_aods, aod_tcslws, tcslw_aod_ps, aod_tcslw_ps = result
    for tau in range(3):
        tcslw_to_aod[tau, i, j] = tcslw_aods[tau]
        aod_to_tcslw[tau, i, j] = aod_tcslws[tau]
        tcslw_to_aod_sig[tau, i, j] = tcslw_aod_ps[tau] < pc_alpha
        aod_to_tcslw_sig[tau, i, j] = aod_tcslw_ps[tau] < pc_alpha

# 修改保存函数以支持多层数据
def save_to_nc(data, sig_data, var_name, description):
    ds = xr.Dataset(
        {
            f"{var_name}_tau1": (["lat", "lon"], data[0]),  # 1个月滞后
            f"{var_name}_tau2": (["lat", "lon"], data[1]),  # 2个月滞后
            f"{var_name}_tau3": (["lat", "lon"], data[2]),  # 3个月滞后
            f"{var_name}_sig_tau1": (["lat", "lon"], sig_data[0]),
            f"{var_name}_sig_tau2": (["lat", "lon"], sig_data[1]),
            f"{var_name}_sig_tau3": (["lat", "lon"], sig_data[2])
        },
        coords={
            "lat": lats,
            "lon": lons
        }
    )
    # 为每个变量添加属性
    for tau in [1, 2, 3]:
        ds[f"{var_name}_tau{tau}"].attrs = {
            'long_name': f'{description} (tau={tau})',
            'units': 'MCI value',
            'valid_range': [-1.0, 1.0]
        }
        ds[f"{var_name}_sig_tau{tau}"].attrs = {
            'long_name': f'Significance flag for {description} (tau={tau})',
            'units': 'Boolean'
        }
    ds.attrs = {
        'title': 'Causal Analysis Results with Significance (Multiple Tau)',
        'source': 'PCMCI method',
        'history': f'Created {pd.Timestamp.now()}'
    }
    ds.to_netcdf(f'{data_dir}/{var_name}_multi_tau_causal_effect.nc')


# 保存结果
save_to_nc(tcslw_to_aod, tcslw_to_aod_sig, 'tcslw_to_aod', 'Causal effect of TCSLW on AOD550')
save_to_nc(aod_to_tcslw, aod_to_tcslw_sig, 'aod_to_tcslw', 'Causal effect of AOD550 on TCSLW')

# 移除绘图部分
print("处理完成！")