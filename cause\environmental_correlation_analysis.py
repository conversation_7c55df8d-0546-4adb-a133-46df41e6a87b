import xarray as xr
import numpy as np
import matplotlib.pyplot as plt
from scipy.stats import pearsonr

def environmental_correlation_analysis():
    """
    分析因果效应与环境因子的关联
    """
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
    plt.rcParams['axes.unicode_minus'] = False
    
    # 加载数据
    causal_ds = xr.open_dataset('D:/dataset/supercooled/aod_to_tcslw_multi_tau_causal_effect.nc')
    aod_ds = xr.open_dataset('D:/dataset/supercooled/aodmean.nc')
    
    causal_effect = causal_ds['aod_to_tcslw_tau1']
    significance = causal_ds['aod_to_tcslw_sig_tau1']
    
    # 插值AOD到因果效应网格
    aod_interp = aod_ds['aod550'].interp(
        latitude=causal_ds['lat'],
        longitude=causal_ds['lon'],
        method='nearest'
    ).squeeze()
    
    # 只分析显著区域
    significant_mask = significance == 1
    causal_sig = causal_effect.where(significant_mask)
    aod_sig = aod_interp.where(significant_mask)
    
    # 1. AOD水平与因果效应的关系
    print("=== AOD水平与因果效应关系 ===")
    
    # 将数据展平并移除NaN
    causal_flat = causal_sig.values.flatten()
    aod_flat = aod_sig.values.flatten()
    
    valid_mask = ~(np.isnan(causal_flat) | np.isnan(aod_flat))
    causal_valid = causal_flat[valid_mask]
    aod_valid = aod_flat[valid_mask]
    
    if len(causal_valid) > 0:
        correlation, p_value = pearsonr(aod_valid, causal_valid)
        print(f"AOD与因果效应的相关系数: {correlation:.4f}, p值: {p_value:.4e}")
        
        # AOD分段分析
        aod_bins = np.percentile(aod_valid, [0, 25, 50, 75, 100])
        print(f"\nAOD分位数: {aod_bins}")
        
        for i in range(len(aod_bins)-1):
            mask = (aod_valid >= aod_bins[i]) & (aod_valid < aod_bins[i+1])
            if i == len(aod_bins)-2:  # 最后一个区间包含上界
                mask = (aod_valid >= aod_bins[i]) & (aod_valid <= aod_bins[i+1])
            
            if np.sum(mask) > 0:
                causal_subset = causal_valid[mask]
                pos_ratio = np.sum(causal_subset > 0) / len(causal_subset) * 100
                neg_ratio = np.sum(causal_subset < 0) / len(causal_subset) * 100
                mean_effect = np.mean(causal_subset)
                
                print(f"AOD {aod_bins[i]:.3f}-{aod_bins[i+1]:.3f}: "
                      f"正因果{pos_ratio:.1f}%, 负因果{neg_ratio:.1f}%, "
                      f"平均效应{mean_effect:.4f}")
    
    # 2. 地理位置分析
    print("\n=== 地理位置分析 ===")
    
    # 按纬度分析
    lat_bands = [
        (-90, -60, "南极地区"),
        (-60, -30, "南半球中高纬"),
        (-30, 0, "南半球低纬"),
        (0, 30, "北半球低纬"),
        (30, 60, "北半球中高纬"),
        (60, 90, "北极地区")
    ]
    
    for lat_min, lat_max, name in lat_bands:
        lat_mask = (causal_ds.lat >= lat_min) & (causal_ds.lat < lat_max)
        if lat_mask.sum() > 0:
            causal_band = causal_sig.isel(lat=lat_mask)
            if causal_band.count() > 0:
                pos_ratio = (causal_band > 0).sum() / causal_band.count() * 100
                neg_ratio = (causal_band < 0).sum() / causal_band.count() * 100
                mean_effect = causal_band.mean().item()
                print(f"{name}: 正因果{pos_ratio:.1f}%, 负因果{neg_ratio:.1f}%, "
                      f"平均{mean_effect:.4f}")
    
    # 3. 绘制分析图
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    
    # 散点图：AOD vs 因果效应
    ax1 = axes[0, 0]
    if len(causal_valid) > 0:
        # 随机采样以避免过多点
        if len(causal_valid) > 10000:
            indices = np.random.choice(len(causal_valid), 10000, replace=False)
            aod_sample = aod_valid[indices]
            causal_sample = causal_valid[indices]
        else:
            aod_sample = aod_valid
            causal_sample = causal_valid
            
        ax1.scatter(aod_sample, causal_sample, alpha=0.5, s=1)
        ax1.set_xlabel('AOD550')
        ax1.set_ylabel('因果效应')
        ax1.set_title(f'AOD vs 因果效应\n相关系数: {correlation:.3f}')
        ax1.grid(True, alpha=0.3)
        ax1.axhline(y=0, color='r', linestyle='--', alpha=0.7)
    
    # AOD分段箱线图
    ax2 = axes[0, 1]
    if len(causal_valid) > 0:
        aod_quartiles = np.percentile(aod_valid, [0, 25, 50, 75, 100])
        causal_groups = []
        labels = []
        
        for i in range(len(aod_quartiles)-1):
            mask = (aod_valid >= aod_quartiles[i]) & (aod_valid < aod_quartiles[i+1])
            if i == len(aod_quartiles)-2:
                mask = (aod_valid >= aod_quartiles[i]) & (aod_valid <= aod_quartiles[i+1])
            
            if np.sum(mask) > 0:
                causal_groups.append(causal_valid[mask])
                labels.append(f'Q{i+1}\n({aod_quartiles[i]:.3f}-{aod_quartiles[i+1]:.3f})')
        
        ax2.boxplot(causal_groups, labels=labels)
        ax2.set_ylabel('因果效应')
        ax2.set_title('不同AOD水平的因果效应分布')
        ax2.grid(True, alpha=0.3)
        ax2.axhline(y=0, color='r', linestyle='--', alpha=0.7)
    
    # 纬度平均
    ax3 = axes[0, 2]
    lat_mean = causal_sig.mean(dim='lon')
    ax3.plot(lat_mean.lat, lat_mean, 'b-', linewidth=2)
    ax3.axhline(y=0, color='r', linestyle='--', alpha=0.7)
    ax3.set_xlabel('纬度')
    ax3.set_ylabel('因果效应')
    ax3.set_title('纬度平均因果效应')
    ax3.grid(True, alpha=0.3)
    
    # 经度平均
    ax4 = axes[1, 0]
    lon_mean = causal_sig.mean(dim='lat')
    ax4.plot(lon_mean.lon, lon_mean, 'g-', linewidth=2)
    ax4.axhline(y=0, color='r', linestyle='--', alpha=0.7)
    ax4.set_xlabel('经度')
    ax4.set_ylabel('因果效应')
    ax4.set_title('经度平均因果效应')
    ax4.grid(True, alpha=0.3)
    
    # 正负因果区域统计
    ax5 = axes[1, 1]
    pos_effect = causal_sig.where(causal_sig > 0)
    neg_effect = causal_sig.where(causal_sig < 0)
    
    pos_count = pos_effect.count().item()
    neg_count = neg_effect.count().item()
    
    ax5.bar(['正因果', '负因果'], [pos_count, neg_count], 
            color=['red', 'blue'], alpha=0.7)
    ax5.set_ylabel('网格点数量')
    ax5.set_title('正负因果区域统计')
    
    # 因果效应强度分布
    ax6 = axes[1, 2]
    if len(causal_valid) > 0:
        ax6.hist(causal_valid, bins=50, alpha=0.7, color='purple', density=True)
        ax6.axvline(x=0, color='r', linestyle='--', alpha=0.7)
        ax6.set_xlabel('因果效应')
        ax6.set_ylabel('概率密度')
        ax6.set_title('因果效应强度分布')
        ax6.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('environmental_correlation_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()

if __name__ == "__main__":
    environmental_correlation_analysis()
