import os
import rarfile
import zipfile

# 源目录和目标目录
src_dir = r'D:\dataset\slw\张宇'
dst_dir = r'D:\dataset\slw\2024112719'

# 确保目标目录存在
os.makedirs(dst_dir, exist_ok=True)

# 遍历所有rar文件
for file in os.listdir(src_dir):
    if file.lower().endswith('.rar'):  # 先不改扩展名
        zip_path = os.path.join(src_dir, file)
        try:
            with zipfile.ZipFile(zip_path) as zf:
                zf.extractall(dst_dir)
            print(f"解压完成: {file}")
        except Exception as e:
            print(f"解压失败: {file}, 错误信息: {e}")

print("全部解压完成！") 