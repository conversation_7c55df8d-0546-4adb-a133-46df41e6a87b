import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
import cartopy.crs as ccrs
import cartopy.feature as cfeature

def calculate_vertical_slw(ds, min_height=600, max_height=1200):
    """
    计算垂直过冷水含量
    参数:
    - ds: xarray Dataset，包含过冷水数据
    - min_height: 最小层状云厚度 (m), 基于<PERSON> et al. (2005)研究
    - max_height: 最大层状云厚度 (m), 基于<PERSON> et al. (2005)研究
    """
    # 假设数据中包含垂直层信息
    # 将总过冷水含量除以云层厚度得到平均密度
    total_slw = ds['tcslw']  # 总过冷水含量 (kg/m²)
    
    # 计算不同云层厚度下的过冷水密度
    min_density = total_slw / max_height  # kg/m³
    max_density = total_slw / min_height  # kg/m³
    
    return min_density, max_density

def plot_vertical_distribution(min_density, max_density, lat, lon):
    """
    绘制垂直分布图
    """
    fig = plt.figure(figsize=(15, 10))
    
    # 绘制最小密度分布
    ax1 = plt.subplot(211, projection=ccrs.PlateCarree())
    plt.pcolormesh(lon, lat, min_density, transform=ccrs.PlateCarree(), 
                   cmap='Blues', vmin=0, vmax=0.001)
    ax1.coastlines()
    ax1.add_feature(cfeature.BORDERS, linestyle=':')
    plt.colorbar(label='最小过冷水密度 (kg/m³)')
    plt.title('最小过冷水密度分布 (1200m云层)')
    
    # 绘制最大密度分布
    ax2 = plt.subplot(212, projection=ccrs.PlateCarree())
    plt.pcolormesh(lon, lat, max_density, transform=ccrs.PlateCarree(), 
                   cmap='Blues', vmin=0, vmax=0.003)
    ax2.coastlines()
    ax2.add_feature(cfeature.BORDERS, linestyle=':')
    plt.colorbar(label='最大过冷水密度 (kg/m³)')
    plt.title('最大过冷水密度分布 (600m云层)')
    
    plt.tight_layout()
    plt.savefig('vertical_slw_distribution.png', dpi=300, bbox_inches='tight')
    plt.close()

def main():
    # 加载数据
    data_path = r'D:\dataset\supercooled\03-23supercooledwater.nc'
    ds = xr.open_dataset(data_path)
    
    # 计算垂直分布
    min_density, max_density = calculate_vertical_slw(ds)
    
    # 获取经纬度信息
    lat = ds.lat.values
    lon = ds.lon.values
    
    # 绘制分布图
    plot_vertical_distribution(min_density, max_density, lat, lon)
    
    # 输出统计信息
    print(f"统计信息:")
    print(f"最小密度平均值: {np.nanmean(min_density):.6f} kg/m³")
    print(f"最大密度平均值: {np.nanmean(max_density):.6f} kg/m³")
    print(f"最小密度最大值: {np.nanmax(min_density):.6f} kg/m³")
    print(f"最大密度最大值: {np.nanmax(max_density):.6f} kg/m³")

if __name__ == "__main__":
    main() 