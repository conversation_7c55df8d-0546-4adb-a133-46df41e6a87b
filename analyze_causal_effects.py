import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
import cartopy.crs as ccrs
import cartopy.feature as cfeature
from matplotlib.colors import TwoSlopeNorm

def main():
    """
    分析AOD对TCSLW和TCIW的lag1month因果效应，验证相反趋势假设
    """
    # 数据文件路径
    tcslw_file = r'D:\dataset\supercooled\aod_to_tcslw_multi_tau_causal_effect.nc'
    tciw_file = r'D:\dataset\supercooled\aod_to_tciw_causal_effect_with_sig.nc'
    
    print("正在读取数据...")
    
    # 读取数据
    tcslw_data = xr.open_dataset(tcslw_file)
    tciw_data = xr.open_dataset(tciw_file)
    
    print("TCSLW数据变量:", list(tcslw_data.variables.keys()))
    print("TCIW数据变量:", list(tciw_data.variables.keys()))
    
    # 自动查找lag1month相关变量
    tcslw_vars = list(tcslw_data.variables.keys())
    tciw_vars = list(tciw_data.variables.keys())
    
    # 查找因果效应和p值变量
    tcslw_causal = None
    tcslw_pvalue = None
    tciw_causal = None
    tciw_pvalue = None
    
    # 查找TCSLW变量 - tau1对应lag1month
    for var in tcslw_vars:
        if 'tau1' in var.lower():
            if 'sig' not in var.lower():
                tcslw_causal = tcslw_data[var]
                print(f"找到TCSLW因果效应: {var}")
            else:
                tcslw_pvalue = tcslw_data[var]
                print(f"找到TCSLW p值: {var}")

    # 查找TCIW变量
    for var in tciw_vars:
        if 'aod_to_tciw' in var.lower():
            if 'sig' not in var.lower():
                tciw_causal = tciw_data[var]
                print(f"找到TCIW因果效应: {var}")
            else:
                tciw_pvalue = tciw_data[var]
                print(f"找到TCIW p值: {var}")
    
    if tcslw_causal is None or tciw_causal is None:
        print("错误: 无法找到因果效应数据")
        return
    
    if tcslw_pvalue is None or tciw_pvalue is None:
        print("错误: 无法找到p值数据")
        return
    
    # 分析显著区域 (p < 0.05)
    alpha = 0.05
    tcslw_sig_mask = tcslw_pvalue < alpha
    tciw_sig_mask = tciw_pvalue < alpha
    
    # 提取显著区域的因果效应
    tcslw_sig = tcslw_causal.where(tcslw_sig_mask)
    tciw_sig = tciw_causal.where(tciw_sig_mask)
    
    # 统计分析
    tcslw_pos = np.sum((tcslw_sig > 0).values)
    tcslw_neg = np.sum((tcslw_sig < 0).values)
    tciw_pos = np.sum((tciw_sig > 0).values)
    tciw_neg = np.sum((tciw_sig < 0).values)
    
    print(f"\n=== 显著区域统计 (p<0.05) ===")
    print(f"TCSLW - 正效应: {tcslw_pos}, 负效应: {tcslw_neg}")
    print(f"TCIW - 正效应: {tciw_pos}, 负效应: {tciw_neg}")
    
    # 创建对比图
    fig = plt.figure(figsize=(18, 10))
    projection = ccrs.PlateCarree()
    
    # 子图1: TCSLW显著区域
    ax1 = plt.subplot(2, 3, 1, projection=projection)
    ax1.set_global()
    ax1.add_feature(cfeature.COASTLINE)
    ax1.add_feature(cfeature.BORDERS)
    
    vmax1 = max(abs(np.nanmin(tcslw_sig)), abs(np.nanmax(tcslw_sig)))
    norm1 = TwoSlopeNorm(vmin=-vmax1, vcenter=0, vmax=vmax1)
    im1 = tcslw_sig.plot(ax=ax1, transform=projection, cmap='RdBu_r', 
                         norm=norm1, add_colorbar=False)
    plt.colorbar(im1, ax=ax1, shrink=0.7)
    ax1.set_title('TCSLW Lag1Month\n(显著区域)', fontsize=12)
    
    # 子图2: TCIW显著区域
    ax2 = plt.subplot(2, 3, 2, projection=projection)
    ax2.set_global()
    ax2.add_feature(cfeature.COASTLINE)
    ax2.add_feature(cfeature.BORDERS)
    
    vmax2 = max(abs(np.nanmin(tciw_sig)), abs(np.nanmax(tciw_sig)))
    norm2 = TwoSlopeNorm(vmin=-vmax2, vcenter=0, vmax=vmax2)
    im2 = tciw_sig.plot(ax=ax2, transform=projection, cmap='RdBu_r', 
                        norm=norm2, add_colorbar=False)
    plt.colorbar(im2, ax=ax2, shrink=0.7)
    ax2.set_title('TCIW Lag1Month\n(显著区域)', fontsize=12)
    
    # 子图3: 散点图对比
    ax3 = plt.subplot(2, 3, 3)
    
    # 找到两者都显著的区域
    both_sig = tcslw_sig_mask & tciw_sig_mask
    tcslw_vals = tcslw_causal.where(both_sig).values.flatten()
    tciw_vals = tciw_causal.where(both_sig).values.flatten()
    
    # 移除NaN
    valid = ~(np.isnan(tcslw_vals) | np.isnan(tciw_vals))
    tcslw_vals = tcslw_vals[valid]
    tciw_vals = tciw_vals[valid]
    
    corr = np.nan
    if len(tcslw_vals) > 0:
        ax3.scatter(tcslw_vals, tciw_vals, alpha=0.6, s=30)
        ax3.plot([-1, 1], [-1, 1], 'r--', label='y=x (正相关)')
        ax3.plot([-1, 1], [1, -1], 'g--', label='y=-x (负相关)')
        
        # 计算相关系数
        corr = np.corrcoef(tcslw_vals, tciw_vals)[0, 1]
        ax3.text(0.05, 0.95, f'相关系数: {corr:.3f}', 
                transform=ax3.transAxes, fontsize=12,
                bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
        
        ax3.set_xlabel('TCSLW 因果效应')
        ax3.set_ylabel('TCIW 因果效应')
        ax3.set_title('两者都显著区域的对比')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
    
    # 子图4: 直方图对比
    ax4 = plt.subplot(2, 3, 4)
    if len(tcslw_vals) > 0:
        ax4.hist(tcslw_vals, bins=20, alpha=0.7, label='TCSLW', color='blue', density=True)
        ax4.hist(tciw_vals, bins=20, alpha=0.7, label='TCIW', color='red', density=True)
        ax4.axvline(0, color='black', linestyle='--', alpha=0.8)
        ax4.set_xlabel('因果效应值')
        ax4.set_ylabel('密度')
        ax4.set_title('效应值分布对比')
        ax4.legend()
        ax4.grid(True, alpha=0.3)
    
    # 子图5: 统计柱状图
    ax5 = plt.subplot(2, 3, 5)
    categories = ['TCSLW正', 'TCSLW负', 'TCIW正', 'TCIW负']
    values = [tcslw_pos, tcslw_neg, tciw_pos, tciw_neg]
    colors = ['blue', 'lightblue', 'red', 'pink']
    
    bars = ax5.bar(categories, values, color=colors)
    ax5.set_ylabel('显著点数')
    ax5.set_title('正负效应点数对比')
    ax5.grid(True, alpha=0.3)
    
    # 在柱子上添加数值
    for bar, value in zip(bars, values):
        ax5.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(values)*0.01,
                str(int(value)), ha='center', va='bottom')
    
    # 子图6: 统计摘要
    ax6 = plt.subplot(2, 3, 6)
    ax6.axis('off')
    
    summary = f"""
显著区域统计摘要 (p<0.05):

TCSLW:
• 总显著点: {tcslw_pos + tcslw_neg}
• 正效应: {tcslw_pos} ({tcslw_pos/(tcslw_pos+tcslw_neg)*100:.1f}%)
• 负效应: {tcslw_neg} ({tcslw_neg/(tcslw_pos+tcslw_neg)*100:.1f}%)
• 平均效应: {np.nanmean(tcslw_sig):.4f}

TCIW:
• 总显著点: {tciw_pos + tciw_neg}
• 正效应: {tciw_pos} ({tciw_pos/(tciw_pos+tciw_neg)*100:.1f}%)
• 负效应: {tciw_neg} ({tciw_neg/(tciw_pos+tciw_neg)*100:.1f}%)
• 平均效应: {np.nanmean(tciw_sig):.4f}

两者都显著区域:
• 共同点数: {len(tcslw_vals)}
• 相关系数: {corr:.4f}

结论: {'负相关，趋势相反' if corr < -0.3 else '相关性较弱' if abs(corr) < 0.3 else '正相关，趋势相同'}
    """
    
    ax6.text(0.05, 0.95, summary, transform=ax6.transAxes, fontsize=10,
             verticalalignment='top', fontfamily='monospace')
    
    plt.tight_layout()
    plt.savefig('causal_effects_comparison.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"\n=== 分析结果 ===")
    print(f"相关系数: {corr:.4f}")
    if corr < -0.3:
        print("结论: TCSLW和TCIW的lag1month因果效应在显著区域呈现负相关，验证了相反趋势的假设！")
    elif abs(corr) < 0.3:
        print("结论: 相关性较弱，可能存在区域差异")
    else:
        print("结论: 呈现正相关，趋势相同")
    
    print("图片已保存为 'causal_effects_comparison.png'")

if __name__ == "__main__":
    main()
