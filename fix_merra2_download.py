#!/usr/bin/env python3
"""
修复MERRA-2下载问题
重新下载太小的文件
"""

import os
import requests
import time
from pathlib import Path
import glob

def setup_session():
    """设置带认证的session"""
    username = "willzhangyu1991"
    password = "Zy.768010991"
    
    session = requests.Session()
    session.auth = (username, password)
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    })
    
    return session

def check_file_size(file_path, min_size_mb=10):
    """检查文件是否太小"""
    if not os.path.exists(file_path):
        return False, 0
    
    size_bytes = os.path.getsize(file_path)
    size_mb = size_bytes / (1024 * 1024)
    
    return size_mb >= min_size_mb, size_mb

def download_file_properly(session, url, file_path, max_retries=3):
    """正确下载文件"""
    for attempt in range(max_retries):
        try:
            print(f"  尝试 {attempt + 1}/{max_retries}...")
            
            # 删除可能存在的损坏文件
            if file_path.exists():
                file_path.unlink()
            
            # 下载文件
            response = session.get(url, stream=True, timeout=600)
            
            # 检查响应
            if response.status_code == 200:
                # 写入文件
                with open(file_path, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:
                            f.write(chunk)
                
                # 验证文件大小
                is_valid, size_mb = check_file_size(file_path, min_size_mb=10)
                if is_valid:
                    print(f"  ✓ 下载成功: {size_mb:.1f} MB")
                    return True
                else:
                    print(f"  ✗ 文件太小: {size_mb:.1f} MB")
                    if file_path.exists():
                        file_path.unlink()
            else:
                print(f"  ✗ HTTP错误: {response.status_code}")
                
        except Exception as e:
            print(f"  ✗ 下载错误: {e}")
            if file_path.exists():
                file_path.unlink()
        
        if attempt < max_retries - 1:
            time.sleep(10)  # 等待更长时间
    
    return False

def main():
    """主函数"""
    print("MERRA-2下载修复工具")
    print("=" * 50)
    
    # 路径配置
    url_file_path = r'D:\willz\download\subset_M2IMNXGAS_5.12.4_20250809_073842_.txt'
    download_dir = Path(r'D:\dataset\supercooled\merra2')
    
    print(f"URL文件: {url_file_path}")
    print(f"下载目录: {download_dir}")
    
    # 检查URL文件
    if not os.path.exists(url_file_path):
        print(f"❌ URL文件不存在: {url_file_path}")
        return
    
    # 读取URL
    print("\n读取下载地址...")
    urls = []
    with open(url_file_path, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if line and (line.startswith('http://') or line.startswith('https://')):
                urls.append(line)
    
    print(f"找到 {len(urls)} 个下载地址")
    
    # 检查现有文件
    print("\n检查现有文件...")
    nc4_files = list(download_dir.glob("*.nc4"))
    print(f"现有文件数: {len(nc4_files)}")
    
    # 找出需要重新下载的文件
    files_to_redownload = []
    total_size = 0
    
    for i, url in enumerate(urls, 1):
        filename = os.path.basename(url.split('?')[0])
        if not filename.endswith('.nc4'):
            filename = f"merra2_aod_{i:04d}.nc4"
        
        file_path = download_dir / filename
        
        if file_path.exists():
            is_valid, size_mb = check_file_size(file_path, min_size_mb=10)
            total_size += size_mb
            
            if not is_valid:
                files_to_redownload.append((url, file_path, size_mb))
        else:
            files_to_redownload.append((url, file_path, 0))
    
    print(f"当前总大小: {total_size:.1f} MB")
    print(f"需要重新下载: {len(files_to_redownload)} 个文件")
    
    if len(files_to_redownload) == 0:
        print("✅ 所有文件都正常！")
        return
    
    # 设置session
    print("\n设置认证...")
    session = setup_session()
    
    # 重新下载文件
    print(f"\n开始重新下载 {len(files_to_redownload)} 个文件...")
    
    success_count = 0
    failed_urls = []
    
    for i, (url, file_path, old_size) in enumerate(files_to_redownload, 1):
        filename = file_path.name
        print(f"\n[{i}/{len(files_to_redownload)}] {filename}")
        print(f"  当前大小: {old_size:.1f} MB")
        
        success = download_file_properly(session, url, file_path)
        
        if success:
            success_count += 1
        else:
            failed_urls.append(url)
            print(f"  ❌ 下载失败")
        
        # 进度报告
        if i % 10 == 0:
            print(f"\n进度: {i}/{len(files_to_redownload)} ({i/len(files_to_redownload)*100:.1f}%)")
            print(f"成功: {success_count}, 失败: {len(failed_urls)}")
        
        # 添加延迟
        time.sleep(3)
    
    print(f"\n重新下载完成:")
    print(f"✅ 成功: {success_count}")
    print(f"❌ 失败: {len(failed_urls)}")
    
    # 保存失败列表
    if failed_urls:
        failed_file = download_dir / "failed_redownload.txt"
        with open(failed_file, 'w') as f:
            for url in failed_urls:
                f.write(url + '\n')
        print(f"失败URL已保存到: {failed_file}")
    
    # 重新检查文件大小
    print(f"\n重新检查文件...")
    nc4_files = list(download_dir.glob("*.nc4"))
    total_size = 0
    valid_files = 0
    
    for file_path in nc4_files:
        is_valid, size_mb = check_file_size(file_path, min_size_mb=10)
        total_size += size_mb
        if is_valid:
            valid_files += 1
    
    print(f"文件总数: {len(nc4_files)}")
    print(f"有效文件: {valid_files}")
    print(f"总大小: {total_size:.1f} MB ({total_size/1024:.1f} GB)")
    
    if total_size > 5000:  # 大于5GB
        print("✅ 文件大小看起来正常了！")
        print("现在可以重新合并文件:")
        print("  cd D:\\dataset\\supercooled\\merra2")
        print("  cdo mergetime *.nc4 ../80-24merra2.nc")
    else:
        print("⚠️  文件大小仍然偏小，可能需要进一步检查")

if __name__ == "__main__":
    main()
