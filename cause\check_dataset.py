import xarray as xr

# 检查79-24tcslw.nc文件的变量
print("检查79-24tcslw.nc文件:")
try:
    ds1 = xr.open_dataset('D:/dataset/supercooled/tciw/79-24tcslw.nc')
    print("变量:", list(ds1.data_vars.keys()))
    print("维度:", list(ds1.dims.keys()))
    print("坐标:", list(ds1.coords.keys()))
    print()
    for var in ds1.data_vars:
        print(f"{var}: {ds1[var].shape}")
    ds1.close()
except Exception as e:
    print(f"错误: {e}")

print("\n" + "="*50 + "\n")

# 检查原来的tcw-tciw-79-24.nc文件的变量
print("检查tcw-tciw-79-24.nc文件:")
try:
    ds2 = xr.open_dataset('D:/dataset/supercooled/tciw/tcw-tciw-79-24.nc')
    print("变量:", list(ds2.data_vars.keys()))
    print("维度:", list(ds2.dims.keys()))
    print("坐标:", list(ds2.coords.keys()))
    print()
    for var in ds2.data_vars:
        print(f"{var}: {ds2[var].shape}")
    ds2.close()
except Exception as e:
    print(f"错误: {e}")
