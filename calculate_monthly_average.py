import xarray as xr
import numpy as np
import matplotlib.pyplot as plt
from joblib import Parallel, delayed
from tqdm import tqdm
from cartopy.mpl.geoaxes import GeoAxes
import cartopy.crs as ccrs
import cartopy.feature as cfeature

# 加载数据
data_path = r'D:\dataset\supercooled\data_0.nc'
ds = xr.open_dataset(data_path)
tcslw = ds['tcslw']  # 提取变量 tcslw

# 计算总月平均
def calculate_monthly_average():
    monthly_avg = tcslw.mean(dim='valid_time')  # 将 'time' 改为 'valid_time'
    return monthly_avg

# 计算纬向平均
def calculate_zonal_mean(data):
    return data.mean(dim='longitude')  # 将 'lon' 改为 'longitude'

# 并行计算
print("开始计算总月平均...")
monthly_avg = calculate_monthly_average()

print("开始计算纬向平均...")
zonal_mean = calculate_zonal_mean(monthly_avg)

# 绘图
plt.figure(figsize=(12, 6))

# 绘制总月平均（使用cartopy绘制二维地图）
ax1 = plt.subplot(1, 2, 1, projection=ccrs.PlateCarree())
monthly_avg.plot(ax=ax1, transform=ccrs.PlateCarree(), cmap='ocean', 
                 cbar_kwargs={'label': 'Total column supercooled liquid water', 'orientation': 'horizontal'})
ax1.coastlines()
ax1.add_feature(cfeature.BORDERS, linestyle=':')
ax1.set_title('Monthly Average Total Column Supercooled Liquid Water')
ax1.set_ylabel('Latitude')  # 标注纬度（英文）
ax1.set_xlabel('Longitude')  # 标注经度（英文）

# 绘制纬向平均（横坐标改为纵坐标，长度变为原来的一半）
ax2 = plt.subplot(1, 2, 2, sharey=ax1)  # 共享y轴
zonal_mean.plot(y='latitude', ax=ax2)
ax2.set_title('Zonal Mean of Total Column Supercooled Liquid Water')
ax2.set_ylabel('')  # 不显示ylabel，与左边图共享


# 调整右边图的宽度
plt.subplots_adjust(right=0.3)  // 减小右边图宽度

plt.tight_layout()
plt.show()

# 保存结果
output_path = r'D:\dataset\supercooled\monthly_avg_tcslw.nc'
monthly_avg.to_netcdf(output_path)
print(f"结果已保存至 {output_path}")