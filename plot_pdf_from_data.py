import numpy as np
import matplotlib.pyplot as plt
from scipy import stats
import pickle

# 设置字体
plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def load_results(filename='analysis_results.pkl'):
    """从文件加载分析结果"""
    try:
        with open(filename, 'rb') as f:
            results = pickle.load(f)
        print(f"Results loaded from {filename}")
        return results
    except FileNotFoundError:
        print(f"File {filename} not found. Please run the analysis first.")
        return None

def plot_pdf_comparison(class1_ratio, class2_ratio, save_plot=True, 
                       figsize=(12, 8), bins=50, alpha=0.7,
                       colors=['blue', 'red'], edgecolors=['black', 'black'],
                       title='PDF Comparison of TCIW/(TCSLW+TCIW) Ratio for Two Classes',
                       xlabel='TCIW/(TCSLW+TCIW) Ratio',
                       ylabel='Probability Density',
                       show_stats=True, show_kde=True, show_median=True):
    """绘制PDF对比图，支持自定义参数"""
    plt.figure(figsize=figsize)
    
    # 绘制第一类区域的PDF
    plt.hist(class1_ratio, bins=bins, alpha=alpha, density=True, 
             label=f'Class 1 (≤0.03737, n={len(class1_ratio)})', 
             color=colors[0], edgecolor=edgecolors[0])
    
    # 绘制第二类区域的PDF
    plt.hist(class2_ratio, bins=bins, alpha=alpha, density=True, 
             label=f'Class 2 (>0.03737, n={len(class2_ratio)})', 
             color=colors[1], edgecolor=edgecolors[1])
    
    # 添加核密度估计
    if show_kde:
        if len(class1_ratio) > 0:
            kde1 = stats.gaussian_kde(class1_ratio)
            x_range = np.linspace(min(class1_ratio), max(class1_ratio), 200)
            plt.plot(x_range, kde1(x_range), color=colors[0], linewidth=2, label='Class 1 KDE')
        
        if len(class2_ratio) > 0:
            kde2 = stats.gaussian_kde(class2_ratio)
            x_range = np.linspace(min(class2_ratio), max(class2_ratio), 200)
            plt.plot(x_range, kde2(x_range), color=colors[1], linewidth=2, label='Class 2 KDE')
    
    # 添加中位数竖线
    if show_median:
        if len(class1_ratio) > 0:
            median1 = np.median(class1_ratio)
            plt.axvline(x=median1, color=colors[0], linestyle='--', linewidth=2, 
                       label=f'Class 1 Median: {median1:.4f}')
        
        if len(class2_ratio) > 0:
            median2 = np.median(class2_ratio)
            plt.axvline(x=median2, color=colors[1], linestyle='--', linewidth=2, 
                       label=f'Class 2 Median: {median2:.4f}')
    
    plt.xlabel(xlabel)
    plt.ylabel(ylabel)
    plt.title(title)
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 添加统计信息
    if show_stats and len(class1_ratio) > 0 and len(class2_ratio) > 0:
        mean1, std1 = np.mean(class1_ratio), np.std(class1_ratio)
        mean2, std2 = np.mean(class2_ratio), np.std(class2_ratio)
        median1 = np.median(class1_ratio)
        median2 = np.median(class2_ratio)
        
        stats_text = f'Class 1: Mean={mean1:.4f}, Median={median1:.4f}, Std={std1:.4f}\n'
        stats_text += f'Class 2: Mean={mean2:.4f}, Median={median2:.4f}, Std={std2:.4f}'
        
        plt.text(0.02, 0.98, stats_text, transform=plt.gca().transAxes, 
                verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    
    plt.tight_layout()
    
    if save_plot:
        plt.savefig('tciw_ratio_pdf_comparison.png', dpi=300, bbox_inches='tight')
        print("Plot saved as tciw_ratio_pdf_comparison.png")
    
    plt.show()

def main():
    """主函数 - 从保存的数据文件绘制图形"""
    # 加载数据
    results = load_results('analysis_results.pkl')
    if results is None:
        return
    
    class1_ratio = results['class1_ratio']
    class2_ratio = results['class2_ratio']
    
    print(f"Class 1 data points: {len(class1_ratio)}")
    print(f"Class 2 data points: {len(class2_ratio)}")
    
    # 绘制图形 - 您可以在这里修改参数
    plot_pdf_comparison(
        class1_ratio, 
        class2_ratio,
        save_plot=True,
        figsize=(12, 8),  # 图形大小
        bins=50,          # 直方图箱数
        alpha=0.7,        # 透明度
        colors=['blue', 'red'],  # 颜色
        edgecolors=['black', 'black'],  # 边框颜色
        title='PDF Comparison of TCIW/(TCSLW+TCIW) Ratio for Two Classes',  # 标题
        xlabel='TCIW/(TCSLW+TCIW) Ratio',  # X轴标签
        ylabel='Probability Density',     # Y轴标签
        show_stats=True,  # 是否显示统计信息
        show_kde=True,    # 是否显示核密度估计
        show_median=True  # 是否显示中位数竖线
    )

if __name__ == "__main__":
    main() 