#!/bin/bash

# MERRA-2 AOD数据下载脚本 (Ubuntu版本)
# 使用方法: bash ubuntu_download_merra2.sh

echo "========================================="
echo "MERRA-2 AOD数据下载脚本 (Ubuntu版本)"
echo "========================================="

# 配置变量
USERNAME="willzhangyu1991"
PASSWORD="Zy.768010991"
URL_FILE="/mnt/d/willz/download/subset_M2IMNXGAS_5.12.4_20250809_073842_.txt"
DOWNLOAD_DIR="/mnt/d/dataset/supercooled/merra2"
OUTPUT_FILE="/mnt/d/dataset/supercooled/80-24aodmerra2.nc"

# 检查wget是否安装
if ! command -v wget &> /dev/null; then
    echo "wget未安装，正在安装..."
    sudo apt update
    sudo apt install -y wget
fi

# 检查Python和xarray是否可用
if ! command -v python3 &> /dev/null; then
    echo "Python3未安装，正在安装..."
    sudo apt install -y python3 python3-pip
fi

# 安装必要的Python包
echo "安装Python依赖包..."
pip3 install xarray netcdf4 numpy --user

# 创建下载目录
echo "创建下载目录: $DOWNLOAD_DIR"
mkdir -p "$DOWNLOAD_DIR"

# 创建.netrc文件用于认证
echo "设置NASA Earthdata认证..."
cat > ~/.netrc << EOF
machine urs.earthdata.nasa.gov
login $USERNAME
password $PASSWORD
EOF

# 设置.netrc文件权限
chmod 600 ~/.netrc

# 检查URL文件是否存在
if [ ! -f "$URL_FILE" ]; then
    echo "错误: URL文件不存在: $URL_FILE"
    echo "请确保文件路径正确"
    exit 1
fi

# 读取URL并下载
echo "开始下载MERRA-2数据..."
echo "URL文件: $URL_FILE"
echo "下载目录: $DOWNLOAD_DIR"

# 计数器
total_urls=$(grep -c "^https\|^http" "$URL_FILE")
current=0
success=0
failed=0

echo "找到 $total_urls 个下载地址"

# 创建失败列表文件
failed_file="$DOWNLOAD_DIR/failed_downloads.txt"
> "$failed_file"

# 逐个下载文件
while IFS= read -r url; do
    # 跳过空行和非URL行
    if [[ ! "$url" =~ ^https?:// ]]; then
        continue
    fi
    
    current=$((current + 1))
    
    # 从URL提取文件名
    filename=$(basename "${url%%\?*}")
    if [[ ! "$filename" =~ \.nc4$ ]]; then
        filename="merra2_aod_$(printf "%04d" $current).nc4"
    fi
    
    filepath="$DOWNLOAD_DIR/$filename"
    
    # 检查文件是否已存在
    if [ -f "$filepath" ] && [ -s "$filepath" ]; then
        echo "[$current/$total_urls] $filename 已存在，跳过"
        success=$((success + 1))
        continue
    fi
    
    echo "[$current/$total_urls] 正在下载: $filename"
    
    # 使用wget下载，最多重试3次
    retry_count=0
    max_retries=3
    download_success=false
    
    while [ $retry_count -lt $max_retries ] && [ "$download_success" = false ]; do
        retry_count=$((retry_count + 1))
        
        if [ $retry_count -gt 1 ]; then
            echo "  重试第 $retry_count 次..."
            sleep 5
        fi
        
        # wget下载命令
        if wget --load-cookies ~/.urs_cookies \
                --save-cookies ~/.urs_cookies \
                --keep-session-cookies \
                --no-check-certificate \
                --auth-no-challenge=on \
                --content-disposition \
                --timeout=300 \
                --tries=1 \
                -O "$filepath" \
                "$url" 2>/dev/null; then
            
            # 检查文件大小
            if [ -f "$filepath" ] && [ -s "$filepath" ]; then
                file_size=$(stat -c%s "$filepath")
                if [ $file_size -gt 1000 ]; then
                    echo "  ✓ 下载成功 (${file_size} bytes)"
                    download_success=true
                    success=$((success + 1))
                else
                    echo "  ✗ 文件太小，可能下载失败"
                    rm -f "$filepath"
                fi
            else
                echo "  ✗ 文件不存在或为空"
                rm -f "$filepath"
            fi
        else
            echo "  ✗ wget下载失败"
            rm -f "$filepath"
        fi
    done
    
    if [ "$download_success" = false ]; then
        echo "  ✗ $filename 下载失败（已重试$max_retries次）"
        echo "$url" >> "$failed_file"
        failed=$((failed + 1))
    fi
    
    # 显示进度
    if [ $((current % 10)) -eq 0 ]; then
        percentage=$((current * 100 / total_urls))
        echo "  进度: $current/$total_urls ($percentage%)"
    fi
    
    # 添加延迟避免过于频繁的请求
    sleep 2
    
done < "$URL_FILE"

echo ""
echo "下载完成！"
echo "✓ 成功: $success 个文件"
echo "✗ 失败: $failed 个文件"

if [ $failed -gt 0 ]; then
    echo "失败的下载地址已保存到: $failed_file"
fi

# 如果有成功下载的文件，进行合并
if [ $success -gt 0 ]; then
    echo ""
    echo "开始合并NetCDF文件..."
    
    # 创建Python合并脚本
    cat > "$DOWNLOAD_DIR/merge_files.py" << 'EOF'
#!/usr/bin/env python3
import xarray as xr
import glob
import os
from pathlib import Path

def merge_merra2_files():
    """合并MERRA-2文件"""
    download_dir = Path("/mnt/d/dataset/supercooled/merra2")
    output_file = "/mnt/d/dataset/supercooled/80-24aodmerra2.nc"
    
    print("正在查找NetCDF文件...")
    nc_files = list(download_dir.glob("*.nc4"))
    
    if len(nc_files) == 0:
        print("没有找到NetCDF文件")
        return
    
    print(f"找到 {len(nc_files)} 个文件")
    
    # 按文件名排序
    nc_files.sort()
    
    datasets = []
    valid_files = []
    
    for file_path in nc_files:
        try:
            ds = xr.open_dataset(file_path)
            datasets.append(ds)
            valid_files.append(file_path)
            print(f"✓ 加载 {file_path.name}")
        except Exception as e:
            print(f"✗ 加载失败 {file_path.name}: {e}")
    
    if len(datasets) == 0:
        print("没有有效的数据集可以合并")
        return
    
    print("正在合并数据集...")
    merged_ds = xr.concat(datasets, dim='time')
    merged_ds = merged_ds.sortby('time')
    
    print(f"正在保存到 {output_file}...")
    merged_ds.to_netcdf(output_file)
    
    # 关闭数据集
    for ds in datasets:
        ds.close()
    merged_ds.close()
    
    print(f"✓ 合并完成！")
    print(f"✓ 输出文件: {output_file}")
    
    # 显示文件信息
    with xr.open_dataset(output_file) as ds:
        print(f"\n合并后的数据集信息:")
        print(f"变量: {list(ds.variables.keys())}")
        print(f"维度: {dict(ds.dims)}")
        if 'time' in ds.dims:
            print(f"时间范围: {ds.time.min().values} 到 {ds.time.max().values}")

if __name__ == "__main__":
    merge_merra2_files()
EOF
    
    # 运行合并脚本
    python3 "$DOWNLOAD_DIR/merge_files.py"
    
    if [ -f "$OUTPUT_FILE" ]; then
        echo ""
        echo "✓ 所有操作完成！"
        echo "✓ 最终文件: $OUTPUT_FILE"
        file_size=$(stat -c%s "$OUTPUT_FILE" 2>/dev/null || echo "未知")
        echo "✓ 文件大小: $file_size bytes"
    else
        echo "✗ 合并过程可能出现问题"
    fi
else
    echo "没有成功下载任何文件，无法进行合并"
fi

echo ""
echo "脚本执行完成！"
