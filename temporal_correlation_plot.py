import xarray as xr
import matplotlib.pyplot as plt
import cartopy.crs as ccrs
import cartopy.feature as cfeature
from matplotlib.colors import LinearSegmentedColormap

# 加载数据
data_path = r'D:\dataset\supercooled\time_correlation.nc'
ds = xr.open_dataset(data_path)
temporal_corr = ds['tcslw']

# 绘图设置
plt.figure(figsize=(14, 7))

# 定义自定义颜色映射（蓝-白-红）
colors = ['#1a35a0', 'white', '#c41212']
cmap = LinearSegmentedColormap.from_list('correlation_cmap', colors)

# 主地图
ax = plt.subplot(projection=ccrs.PlateCarree())

# 地图装饰
ax.coastlines(linewidth=0.8)
ax.add_feature(cfeature.BORDERS, linestyle=':', linewidth=0.5)
ax.add_feature(cfeature.LAND, facecolor='#f0f0f0')
gl = ax.gridlines(crs=ccrs.PlateCarree(), draw_labels=True,
                 linewidth=0.5, color='gray', alpha=0.5, linestyle='--')
gl.top_labels = False
gl.right_labels = False

# 标题和标签
ax.set_title('Correlation between AOD550 and TCSLW', 
            pad=20, fontsize=14, fontweight='bold')
ax.set_ylabel('Latitude', fontsize=12)
ax.set_xlabel('Longitude', fontsize=12)

# 主地图 (修改cbar_kwargs去掉label)
plot = temporal_corr.plot(ax=ax, transform=ccrs.PlateCarree(), cmap=cmap,
                         vmin=-1, vmax=1,
                         cbar_kwargs={
                             'orientation': 'horizontal',
                             'ticks': [-1, -0.5, 0, 0.5, 1],
                             'shrink': 0.8,
                             'pad': 0.1
                         })

# 输出图形
output_path = r"D:\dataset\program\temporal_correlation_plot.png"
try:
    plt.tight_layout()
    plt.savefig(output_path, dpi=300, bbox_inches='tight', facecolor='white')
    print(f"图形已成功保存至: {output_path}")
    plt.show()
except Exception as e:
    print(f"图形保存或显示过程中出现错误: {e}")