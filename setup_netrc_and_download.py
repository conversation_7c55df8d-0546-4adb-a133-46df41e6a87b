import os
import subprocess
import xarray as xr
from pathlib import Path
import time

def setup_netrc_file():
    """设置.netrc文件用于NASA Earthdata认证"""
    print("设置NASA Earthdata认证文件")
    print("=" * 40)
    
    username = "willzhangyu1991"
    password = "Zy.768010991"
    
    # 创建.netrc文件内容
    netrc_content = f"""machine urs.earthdata.nasa.gov
login {username}
password {password}
"""
    
    # Windows和Unix的.netrc文件位置不同
    if os.name == 'nt':  # Windows
        netrc_path = Path.home() / '_netrc'
    else:  # Unix/Linux/Mac
        netrc_path = Path.home() / '.netrc'
    
    try:
        with open(netrc_path, 'w') as f:
            f.write(netrc_content)
        
        # 设置文件权限 (仅所有者可读写)
        if os.name != 'nt':
            os.chmod(netrc_path, 0o600)
        
        print(f"✓ 认证文件已创建: {netrc_path}")
        return True
        
    except Exception as e:
        print(f"创建认证文件失败: {e}")
        return False

def create_wget_script():
    """创建wget下载脚本"""
    url_file_path = r'D:\willz\download\subset_M2IMNXGAS_5.12.4_20250809_073842_.txt'
    download_dir = Path(r'D:\dataset\supercooled\merra2')
    
    # 创建下载目录
    download_dir.mkdir(parents=True, exist_ok=True)
    
    # 读取URL
    try:
        with open(url_file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        urls = []
        for line in lines:
            line = line.strip()
            if line and (line.startswith('http://') or line.startswith('https://')):
                urls.append(line)
        
        print(f"找到 {len(urls)} 个下载地址")
        
    except Exception as e:
        print(f"读取URL文件失败: {e}")
        return False
    
    # 创建wget脚本
    script_path = download_dir / "download_script.bat"
    
    with open(script_path, 'w') as f:
        f.write("@echo off\n")
        f.write("echo Starting MERRA-2 AOD data download...\n")
        f.write(f"cd /d {download_dir}\n")
        
        for i, url in enumerate(urls, 1):
            filename = os.path.basename(url.split('?')[0])
            if not filename.endswith('.nc4'):
                filename = f"merra2_aod_{i:04d}.nc4"
            
            f.write(f'echo Downloading file {i}/{len(urls)}: {filename}\n')
            f.write(f'wget --load-cookies cookies.txt --save-cookies cookies.txt --keep-session-cookies --no-check-certificate --auth-no-challenge=on --content-disposition -O "{filename}" "{url}"\n')
            f.write("if errorlevel 1 echo Download failed for " + filename + "\n")
            f.write("timeout /t 2 /nobreak >nul\n")  # 2秒延迟
    
    print(f"✓ 下载脚本已创建: {script_path}")
    return script_path

def create_manual_instructions():
    """创建手动下载说明"""
    instructions = """
=== MERRA-2 AOD数据下载说明 ===

由于NASA Earthdata的认证机制比较复杂，建议使用以下方法：

方法1: 使用浏览器手动下载
1. 在浏览器中登录 https://urs.earthdata.nasa.gov/
2. 使用用户名: willzhangyu1991
3. 使用密码: Zy.768010991
4. 登录后，逐个访问URL文件中的链接进行下载

方法2: 使用wget (推荐)
1. 安装wget: https://www.gnu.org/software/wget/
2. 运行我们生成的下载脚本: download_script.bat

方法3: 使用curl
1. 创建.netrc文件 (已自动创建)
2. 使用curl命令下载

方法4: 使用专门的工具
1. 安装NASA的官方下载工具
2. 或使用Python的earthaccess库

URL文件位置: D:\\willz\\download\\subset_M2IMNXGAS_5.12.4_20250809_073842_.txt
下载目录: D:\\dataset\\supercooled\\merra2\\
目标文件: D:\\dataset\\supercooled\\80-24aodmerra2.nc

文件数量: 541个
预计大小: 10-25GB
预计时间: 2-6小时
"""
    
    instructions_file = Path(r'D:\dataset\supercooled\merra2') / "download_instructions.txt"
    with open(instructions_file, 'w', encoding='utf-8') as f:
        f.write(instructions)
    
    print(f"✓ 下载说明已保存: {instructions_file}")
    return instructions

def create_merge_script():
    """创建合并脚本"""
    merge_script = '''
import xarray as xr
import glob
from pathlib import Path
import os

def merge_merra2_files():
    """合并MERRA-2文件"""
    download_dir = Path(r'D:\\dataset\\supercooled\\merra2')
    output_file = r'D:\\dataset\\supercooled\\80-24aodmerra2.nc'
    
    print("正在查找NetCDF文件...")
    nc_files = list(download_dir.glob("*.nc4"))
    
    if len(nc_files) == 0:
        print("没有找到NetCDF文件")
        return
    
    print(f"找到 {len(nc_files)} 个文件")
    
    # 按文件名排序
    nc_files.sort()
    
    datasets = []
    valid_files = []
    
    for file_path in nc_files:
        try:
            ds = xr.open_dataset(file_path)
            datasets.append(ds)
            valid_files.append(file_path)
            print(f"✓ 加载 {file_path.name}")
        except Exception as e:
            print(f"✗ 加载失败 {file_path.name}: {e}")
    
    if len(datasets) == 0:
        print("没有有效的数据集可以合并")
        return
    
    print("正在合并数据集...")
    merged_ds = xr.concat(datasets, dim='time')
    merged_ds = merged_ds.sortby('time')
    
    print(f"正在保存到 {output_file}...")
    merged_ds.to_netcdf(output_file)
    
    # 关闭数据集
    for ds in datasets:
        ds.close()
    merged_ds.close()
    
    print(f"✓ 合并完成！")
    print(f"✓ 输出文件: {output_file}")
    
    # 显示文件信息
    with xr.open_dataset(output_file) as ds:
        print(f"\\n合并后的数据集信息:")
        print(f"变量: {list(ds.variables.keys())}")
        print(f"维度: {dict(ds.dims)}")
        if 'time' in ds.dims:
            print(f"时间范围: {ds.time.min().values} 到 {ds.time.max().values}")

if __name__ == "__main__":
    merge_merra2_files()
'''
    
    merge_script_path = Path(r'D:\dataset\supercooled\merra2') / "merge_files.py"
    with open(merge_script_path, 'w', encoding='utf-8') as f:
        f.write(merge_script)
    
    print(f"✓ 合并脚本已创建: {merge_script_path}")
    return merge_script_path

def main():
    """主函数"""
    print("MERRA-2 AOD数据下载准备工具")
    print("=" * 40)
    
    # 设置认证文件
    if setup_netrc_file():
        print("✓ 认证文件设置完成")
    
    # 创建wget脚本
    script_path = create_wget_script()
    if script_path:
        print("✓ wget下载脚本创建完成")
    
    # 创建合并脚本
    merge_path = create_merge_script()
    if merge_path:
        print("✓ 文件合并脚本创建完成")
    
    # 显示说明
    instructions = create_manual_instructions()
    print("\n" + instructions)
    
    print("\n下一步操作:")
    print("1. 如果有wget，运行: D:\\dataset\\supercooled\\merra2\\download_script.bat")
    print("2. 下载完成后，运行: python D:\\dataset\\supercooled\\merra2\\merge_files.py")
    print("3. 或者按照说明文件手动下载")

if __name__ == "__main__":
    main()
