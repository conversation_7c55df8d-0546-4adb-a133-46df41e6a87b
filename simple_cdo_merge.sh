#!/bin/bash

# 简化的CDO合并脚本
# 使用方法: bash simple_cdo_merge.sh

echo "使用CDO合并MERRA-2文件为 80-24merra2.nc"
echo "============================================"

# 配置路径
DOWNLOAD_DIR="/mnt/d/dataset/supercooled/merra2"
OUTPUT_FILE="/mnt/d/dataset/supercooled/80-24merra2.nc"

# 检查CDO
if ! command -v cdo &> /dev/null; then
    echo "安装CDO..."
    sudo apt update && sudo apt install -y cdo
fi

# 检查下载目录
if [ ! -d "$DOWNLOAD_DIR" ]; then
    echo "错误: 目录不存在 $DOWNLOAD_DIR"
    exit 1
fi

cd "$DOWNLOAD_DIR"

# 检查文件
nc_files=(*.nc4)
if [ ! -e "${nc_files[0]}" ]; then
    echo "错误: 没有找到.nc4文件"
    exit 1
fi

echo "找到 ${#nc_files[@]} 个文件"

# 使用CDO合并
echo "开始合并..."
echo "输出文件: $OUTPUT_FILE"

# 直接合并所有文件
cdo mergetime *.nc4 "$OUTPUT_FILE"

if [ $? -eq 0 ]; then
    echo "✓ 合并成功！"
    echo "✓ 文件: $OUTPUT_FILE"
    echo "✓ 大小: $(du -sh "$OUTPUT_FILE" | cut -f1)"
    
    # 显示基本信息
    echo ""
    echo "数据信息:"
    cdo sinfon "$OUTPUT_FILE" | head -5
    
    echo ""
    echo "时间范围:"
    echo "时间步数: $(cdo ntime "$OUTPUT_FILE")"
    
else
    echo "✗ 合并失败"
    exit 1
fi

echo ""
echo "完成！输出文件: $OUTPUT_FILE"
