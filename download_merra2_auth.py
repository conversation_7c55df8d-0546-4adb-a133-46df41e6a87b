import os
import requests
import xarray as xr
from pathlib import Path
import time
import getpass
from requests.auth import HTTPBasicAuth

def setup_earthdata_auth():
    """设置NASA Earthdata认证"""
    print("NASA Earthdata Login Required")
    print("=" * 40)

    # 您可以在这里直接设置凭据，避免交互式输入
    username = "willzhangyu1991"
    password = "Zy.768010991"

    print(f"使用用户名: {username}")
    print("密码已设置")

    return username, password

def create_authenticated_session(username, password):
    """创建带认证的session"""
    session = requests.Session()
    session.auth = HTTPBasicAuth(username, password)
    
    # 设置User-Agent
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    })
    
    return session

def download_with_auth(url, file_path, session, max_retries=3):
    """使用认证下载文件"""
    for attempt in range(max_retries):
        try:
            response = session.get(url, stream=True, timeout=300)
            
            # 处理重定向到登录页面的情况
            if 'urs.earthdata.nasa.gov' in response.url:
                print(f"需要重新认证，尝试第 {attempt + 1} 次...")
                time.sleep(2)
                continue
            
            response.raise_for_status()
            
            # 写入文件
            with open(file_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
            
            return True
            
        except Exception as e:
            print(f"下载尝试 {attempt + 1} 失败: {e}")
            if attempt < max_retries - 1:
                time.sleep(5)
            else:
                return False
    
    return False

def main():
    """主函数"""
    # 配置路径
    url_file_path = input("请输入URL文件的完整路径 (直接回车使用默认路径): ").strip()
    if not url_file_path:
        url_file_path = r'D:\willz\download\subset_M2IMNXGAS_5.12.4_20250809_073842_.txt'
    
    download_dir = Path(r'D:\dataset\supercooled\merra2')
    output_file = r'D:\dataset\supercooled\80-24aodmerra2.nc'
    
    print(f"\nURL文件路径: {url_file_path}")
    print(f"下载目录: {download_dir}")
    print(f"输出文件: {output_file}")
    
    # 创建下载目录
    download_dir.mkdir(parents=True, exist_ok=True)
    
    # 检查URL文件
    if not os.path.exists(url_file_path):
        print(f"错误: 找不到文件 {url_file_path}")
        return
    
    # 读取URL
    print("\n正在读取下载地址...")
    try:
        with open(url_file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        urls = []
        for line in lines:
            line = line.strip()
            if line and (line.startswith('http://') or line.startswith('https://')):
                urls.append(line)
        
        print(f"找到 {len(urls)} 个下载地址")
        
        if len(urls) == 0:
            print("没有找到有效的下载地址")
            return
            
    except Exception as e:
        print(f"读取文件出错: {e}")
        return
    
    # 设置认证
    username, password = setup_earthdata_auth()
    session = create_authenticated_session(username, password)
    
    # 下载文件
    downloaded_files = []
    failed_downloads = []
    
    print(f"\n开始下载 {len(urls)} 个文件...")
    print("注意: 下载可能需要较长时间，请耐心等待...")
    
    for i, url in enumerate(urls, 1):
        try:
            # 从URL提取文件名
            filename = os.path.basename(url.split('?')[0])
            if not filename.endswith('.nc4'):
                filename = f"merra2_aod_{i:04d}.nc4"
            
            file_path = download_dir / filename
            
            # 检查文件是否已存在
            if file_path.exists():
                print(f"[{i}/{len(urls)}] {filename} 已存在，跳过")
                downloaded_files.append(str(file_path))
                continue
            
            print(f"[{i}/{len(urls)}] 正在下载 {filename}...")
            
            # 使用认证下载
            success = download_with_auth(url, file_path, session)
            
            if success:
                downloaded_files.append(str(file_path))
                print(f"[{i}/{len(urls)}] ✓ {filename} 下载完成")
            else:
                failed_downloads.append(url)
                print(f"[{i}/{len(urls)}] ✗ {filename} 下载失败")
            
            # 添加延迟避免过于频繁的请求
            time.sleep(2)
            
        except Exception as e:
            failed_downloads.append(url)
            print(f"[{i}/{len(urls)}] ✗ 下载失败: {e}")
    
    print(f"\n下载完成:")
    print(f"✓ 成功: {len(downloaded_files)} 个文件")
    print(f"✗ 失败: {len(failed_downloads)} 个文件")
    
    if len(downloaded_files) == 0:
        print("没有成功下载任何文件")
        return
    
    # 合并文件
    print(f"\n开始合并 {len(downloaded_files)} 个文件...")
    try:
        datasets = []
        valid_files = []
        
        for file_path in downloaded_files:
            try:
                ds = xr.open_dataset(file_path)
                datasets.append(ds)
                valid_files.append(file_path)
                print(f"✓ 加载 {os.path.basename(file_path)}")
            except Exception as e:
                print(f"✗ 加载失败 {os.path.basename(file_path)}: {e}")
        
        if len(datasets) == 0:
            print("没有有效的数据集可以合并")
            return
        
        print("正在合并数据集...")
        merged_ds = xr.concat(datasets, dim='time')
        merged_ds = merged_ds.sortby('time')
        
        print(f"正在保存到 {output_file}...")
        merged_ds.to_netcdf(output_file)
        
        # 关闭数据集
        for ds in datasets:
            ds.close()
        merged_ds.close()
        
        print(f"\n✓ 处理完成！")
        print(f"✓ 成功下载: {len(downloaded_files)} 个文件")
        print(f"✓ 成功合并: {len(valid_files)} 个文件")
        print(f"✓ 输出文件: {output_file}")
        
        # 显示文件信息
        with xr.open_dataset(output_file) as ds:
            print(f"\n合并后的数据集信息:")
            print(f"变量: {list(ds.variables.keys())}")
            print(f"维度: {dict(ds.dims)}")
            if 'time' in ds.dims:
                print(f"时间范围: {ds.time.min().values} 到 {ds.time.max().values}")
        
        # 保存失败列表
        if failed_downloads:
            failed_file = download_dir / "failed_downloads.txt"
            with open(failed_file, 'w') as f:
                for url in failed_downloads:
                    f.write(url + '\n')
            print(f"\n失败的下载地址已保存到: {failed_file}")
        
    except Exception as e:
        print(f"合并过程出错: {e}")

if __name__ == "__main__":
    main()
