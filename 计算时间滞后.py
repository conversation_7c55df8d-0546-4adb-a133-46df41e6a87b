# -*- coding: utf-8 -*-
import xarray as xr
import numpy as np
import pandas as pd
from tigramite import data_processing as pp
from tigramite.pcmci import PCMCI
from tigramite.independence_tests.parcorr import ParCorr
from joblib import Parallel, delayed
from tqdm import tqdm

# 设置文件路径和参数
data_dir = r'D:\dataset\supercooled'
water_file = '03-23supercooledwater.nc'
aod_file = '03-23aod.nc'

# 加载数据
def load_data(var_file, var_name):
    ds = xr.open_dataset(f'{data_dir}/{var_file}')
    return ds[var_name]

water_data = load_data(water_file, 'tcslw')
aod_data = load_data(aod_file, 'aod550')

# 获取时间维度名称
time_dim = [dim for dim in water_data.dims if 'time' in dim.lower()][0]

# 获取全球格点信息
lats = water_data.lat.values
lons = water_data.lon.values

# 创建结果存储字典
tau_list = [1, 2, 3]
results_dict = {
    'aod550_to_tcslw': {tau: np.zeros((len(lats), len(lons))) for tau in tau_list},
    'tcslw_to_aod550': {tau: np.zeros((len(lats), len(lons))) for tau in tau_list},
    'aod550_to_tcslw_sig': {tau: np.zeros((len(lats), len(lons))) for tau in tau_list},
    'tcslw_to_aod550_sig': {tau: np.zeros((len(lats), len(lons))) for tau in tau_list}
}

# PCMCI参数设置
parcorr = ParCorr(significance='analytic')
pc_alpha = 0.05

# 格点处理函数
def process_point(i, j, lat, lon):
    try:
        water_ts = water_data.sel(lat=lat, lon=lon).values
        aod_ts = aod_data.sel(lat=lat, lon=lon).values
        
        df = pd.DataFrame({
            'tcslw': water_ts,
            'aod550': aod_ts
        }, index=pd.to_datetime(water_data[time_dim].values))
        
        if df.isnull().any().any():
            df = df.interpolate().dropna()
            
        data = df.values
        dataframe = pp.DataFrame(data, datatime=df.index, var_names=['tcslw', 'aod550'])
        
        pcmci = PCMCI(
            dataframe=dataframe,
            cond_ind_test=parcorr,
            verbosity=0
        )
        
        results = pcmci.run_pcmci(tau_max=max(tau_list), pc_alpha=pc_alpha)
        
        return_dict = {}
        for tau in tau_list:
            return_dict[f'aod550_tcslw_{tau}'] = results['val_matrix'][1, 0, tau]
            return_dict[f'tcslw_aod550_{tau}'] = results['val_matrix'][0, 1, tau]
            return_dict[f'aod550_tcslw_sig_{tau}'] = results['p_matrix'][1, 0, tau] < pc_alpha
            return_dict[f'tcslw_aod550_sig_{tau}'] = results['p_matrix'][0, 1, tau] < pc_alpha
        
        return (i, j, return_dict)
        
    except Exception as e:
        print(f"Error at lat={lat}, lon={lon}: {str(e)}")
        return_dict = {f'{prefix}_{tau}': np.nan for prefix in ['aod550_tcslw', 'tcslw_aod550'] for tau in tau_list}
        return_dict.update({f'{prefix}_sig_{tau}': False for prefix in ['aod550_tcslw', 'tcslw_aod550'] for tau in tau_list})
        return (i, j, return_dict)

# 并行计算主循环
print("开始并行计算...")
results = Parallel(n_jobs=-1)(delayed(process_point)(i, j, lat, lon)
                             for i, lat in enumerate(tqdm(lats, desc="纬度进度"))
                             for j, lon in enumerate(lons))

# 填充结果
for i, j, result_dict in results:
    for tau in tau_list:
        results_dict['aod550_to_tcslw'][tau][i, j] = result_dict[f'aod550_tcslw_{tau}']
        results_dict['tcslw_to_aod550'][tau][i, j] = result_dict[f'tcslw_aod550_{tau}']
        results_dict['aod550_to_tcslw_sig'][tau][i, j] = result_dict[f'aod550_tcslw_sig_{tau}']
        results_dict['tcslw_to_aod550_sig'][tau][i, j] = result_dict[f'tcslw_aod550_sig_{tau}']

# 保存结果到NetCDF
def save_to_nc(data_dict, sig_dict, var_name_prefix):
    data_vars = {}
    for tau in tau_list:
        data_vars[f'{var_name_prefix}_tau{tau}'] = (['lat', 'lon'], data_dict[tau])
        data_vars[f'{var_name_prefix}_tau{tau}_sig'] = (['lat', 'lon'], sig_dict[tau])
    
    ds = xr.Dataset(
        data_vars,
        coords={
            'lat': lats,
            'lon': lons
        }
    )
    ds.to_netcdf(f'{data_dir}/{var_name_prefix}_multi_tau.nc')

# 保存结果
save_to_nc(results_dict['aod550_to_tcslw'], results_dict['aod550_to_tcslw_sig'], 'aod550_to_tcslw')
save_to_nc(results_dict['tcslw_to_aod550'], results_dict['tcslw_to_aod550_sig'], 'tcslw_to_aod550')

print("处理完成！结果已保存为NC文件。")