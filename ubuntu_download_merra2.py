#!/usr/bin/env python3
"""
MERRA-2 AOD数据下载脚本 (Ubuntu版本)
使用方法: python3 ubuntu_download_merra2.py
"""

import os
import subprocess
import time
from pathlib import Path
import requests
import xarray as xr

def setup_environment():
    """设置环境和依赖"""
    print("检查和安装依赖...")
    
    # 检查wget
    try:
        subprocess.run(['wget', '--version'], capture_output=True, check=True)
        print("✓ wget已安装")
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("安装wget...")
        subprocess.run(['sudo', 'apt', 'update'], check=True)
        subprocess.run(['sudo', 'apt', 'install', '-y', 'wget'], check=True)
    
    # 安装Python包
    try:
        import xarray
        import netCDF4
        print("✓ Python依赖已安装")
    except ImportError:
        print("安装Python依赖...")
        subprocess.run(['pip3', 'install', 'xarray', 'netcdf4', 'numpy', '--user'], check=True)

def setup_netrc():
    """设置.netrc认证文件"""
    username = "willzhangyu1991"
    password = "Zy.768010991"
    
    netrc_content = f"""machine urs.earthdata.nasa.gov
login {username}
password {password}
"""
    
    netrc_path = Path.home() / '.netrc'
    with open(netrc_path, 'w') as f:
        f.write(netrc_content)
    
    # 设置权限
    os.chmod(netrc_path, 0o600)
    print(f"✓ 认证文件已创建: {netrc_path}")

def download_file_with_wget(url, filepath, max_retries=3):
    """使用wget下载单个文件"""
    for attempt in range(max_retries):
        try:
            if attempt > 0:
                print(f"  重试第 {attempt + 1} 次...")
                time.sleep(5)
            
            cmd = [
                'wget',
                '--load-cookies', str(Path.home() / '.urs_cookies'),
                '--save-cookies', str(Path.home() / '.urs_cookies'),
                '--keep-session-cookies',
                '--no-check-certificate',
                '--auth-no-challenge=on',
                '--content-disposition',
                '--timeout=300',
                '--tries=1',
                '-O', str(filepath),
                url
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0 and filepath.exists() and filepath.stat().st_size > 1000:
                print(f"  ✓ 下载成功 ({filepath.stat().st_size} bytes)")
                return True
            else:
                if filepath.exists():
                    filepath.unlink()
                print(f"  ✗ 下载失败: {result.stderr}")
                
        except Exception as e:
            print(f"  ✗ 下载异常: {e}")
            if filepath.exists():
                filepath.unlink()
    
    return False

def main():
    """主函数"""
    print("=" * 50)
    print("MERRA-2 AOD数据下载脚本 (Ubuntu版本)")
    print("=" * 50)
    
    # 配置路径 - 使用WSL的Windows路径映射
    url_file_path = Path("/mnt/d/willz/download/subset_M2IMNXGAS_5.12.4_20250809_073842_.txt")
    download_dir = Path("/mnt/d/dataset/supercooled/merra2")
    output_file = Path("/mnt/d/dataset/supercooled/80-24merra2.nc")
    
    print(f"URL文件: {url_file_path}")
    print(f"下载目录: {download_dir}")
    print(f"输出文件: {output_file}")
    
    # 设置环境
    setup_environment()
    setup_netrc()
    
    # 创建下载目录
    download_dir.mkdir(parents=True, exist_ok=True)
    
    # 检查URL文件
    if not url_file_path.exists():
        print(f"错误: URL文件不存在: {url_file_path}")
        return
    
    # 读取URL
    print("\n正在读取下载地址...")
    urls = []
    with open(url_file_path, 'r') as f:
        for line in f:
            line = line.strip()
            if line and (line.startswith('http://') or line.startswith('https://')):
                urls.append(line)
    
    print(f"找到 {len(urls)} 个下载地址")
    
    # 下载文件
    downloaded_files = []
    failed_downloads = []
    
    print(f"\n开始下载 {len(urls)} 个文件...")
    start_time = time.time()
    
    for i, url in enumerate(urls, 1):
        # 从URL提取文件名
        filename = os.path.basename(url.split('?')[0])
        if not filename.endswith('.nc4'):
            filename = f"merra2_aod_{i:04d}.nc4"
        
        filepath = download_dir / filename
        
        # 检查文件是否已存在
        if filepath.exists() and filepath.stat().st_size > 1000:
            print(f"[{i}/{len(urls)}] {filename} 已存在，跳过")
            downloaded_files.append(str(filepath))
            continue
        
        print(f"[{i}/{len(urls)}] 正在下载: {filename}")
        
        # 下载文件
        success = download_file_with_wget(url, filepath)
        
        if success:
            downloaded_files.append(str(filepath))
        else:
            failed_downloads.append(url)
            print(f"[{i}/{len(urls)}] ✗ {filename} 下载失败")
        
        # 显示进度
        if i % 10 == 0:
            elapsed = time.time() - start_time
            avg_time = elapsed / i
            remaining = (len(urls) - i) * avg_time
            print(f"  进度: {i}/{len(urls)} ({i/len(urls)*100:.1f}%), 预计剩余: {remaining/3600:.1f}小时")
        
        # 添加延迟
        time.sleep(2)
    
    print(f"\n下载完成:")
    print(f"✓ 成功: {len(downloaded_files)} 个文件")
    print(f"✗ 失败: {len(failed_downloads)} 个文件")
    
    # 保存失败列表
    if failed_downloads:
        failed_file = download_dir / "failed_downloads.txt"
        with open(failed_file, 'w') as f:
            for url in failed_downloads:
                f.write(url + '\n')
        print(f"失败的下载地址已保存到: {failed_file}")
    
    # 合并文件
    if downloaded_files:
        print(f"\n开始合并 {len(downloaded_files)} 个文件...")
        try:
            datasets = []
            valid_files = []
            
            for file_path in downloaded_files:
                try:
                    ds = xr.open_dataset(file_path)
                    datasets.append(ds)
                    valid_files.append(file_path)
                    print(f"✓ 加载 {os.path.basename(file_path)}")
                except Exception as e:
                    print(f"✗ 加载失败 {os.path.basename(file_path)}: {e}")
            
            if datasets:
                print("正在合并数据集...")
                merged_ds = xr.concat(datasets, dim='time')
                merged_ds = merged_ds.sortby('time')
                
                print(f"正在保存到 {output_file}...")
                merged_ds.to_netcdf(output_file)
                
                # 关闭数据集
                for ds in datasets:
                    ds.close()
                merged_ds.close()
                
                print(f"\n✓ 处理完成！")
                print(f"✓ 成功下载: {len(downloaded_files)} 个文件")
                print(f"✓ 成功合并: {len(valid_files)} 个文件")
                print(f"✓ 输出文件: {output_file}")
                
                # 显示文件信息
                with xr.open_dataset(output_file) as ds:
                    print(f"\n合并后的数据集信息:")
                    print(f"变量: {list(ds.variables.keys())}")
                    print(f"维度: {dict(ds.dims)}")
                    if 'time' in ds.dims:
                        print(f"时间范围: {ds.time.min().values} 到 {ds.time.max().values}")
            else:
                print("没有有效的数据集可以合并")
        
        except Exception as e:
            print(f"合并过程出错: {e}")
    else:
        print("没有成功下载任何文件")

if __name__ == "__main__":
    main()
