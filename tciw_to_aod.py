# -*- coding: utf-8 -*-
"""
Created on Fri Mar 21 08:54:02 2025

@author: willz
"""

import xarray as xr
import numpy as np
import pandas as pd
import cartopy.crs as ccrs
import matplotlib.pyplot as plt
from tigramite import data_processing as pp
from tigramite.pcmci import PCMCI
from tigramite.independence_tests.parcorr import ParCorr
from joblib import Parallel, delayed
from tqdm import tqdm

# 设置文件路径和参数
data_dir = r'D:\dataset\supercooled'
water_file = 'reice03-23.nc'
aod_file = '03-23aod.nc'

# 加载数据
def load_data(var_file, var_name):
    ds = xr.open_dataset(f'{data_dir}/{var_file}')
    return ds[var_name]

water_data = load_data(water_file, 'tciw')
aod_data = load_data(aod_file, 'aod550')

# 获取时间维度名称
time_dim = [dim for dim in water_data.dims if 'time' in dim.lower()][0]

# 获取全球格点信息
lats = water_data.lat.values
lons = water_data.lon.values

# 创建空数组存储结果
tciw_to_aod = np.zeros((len(lats), len(lons)))
aod_to_tciw = np.zeros((len(lats), len(lons)))
tciw_to_aod_sig = np.zeros((len(lats), len(lons)))
aod_to_tciw_sig = np.zeros((len(lats), len(lons)))

# PCMCI参数设置
parcorr = ParCorr(significance='analytic')
tau_max = 6
pc_alpha = 0.05

# 格点处理函数
def process_point(i, j, lat, lon):
    try:
        water_ts = water_data.sel(lat=lat, lon=lon).values
        aod_ts = aod_data.sel(lat=lat, lon=lon).values
        
        df = pd.DataFrame({
            'tciw': water_ts,
            'aod550': aod_ts
        }, index=pd.to_datetime(water_data[time_dim].values))
        
        if df.isnull().any().any():
            df = df.interpolate().dropna()
            
        data = df.values
        dataframe = pp.DataFrame(data, datatime=df.index, var_names=['tciw', 'aod550'])
        
        pcmci = PCMCI(
            dataframe=dataframe,
            cond_ind_test=parcorr,
            verbosity=0
        )
        
        results = pcmci.run_pcmci(tau_max=tau_max, pc_alpha=pc_alpha)
        sig_matrix = results['p_matrix'] < pc_alpha
        
        return i, j, results['val_matrix'][0, 1, 1], results['val_matrix'][1, 0, 1], \
               sig_matrix[0, 1, 1], sig_matrix[1, 0, 1]
        
    except Exception as e:
        print(f"Error at lat={lat}, lon={lon}: {str(e)}")
        return i, j, np.nan, np.nan, False, False

# 并行计算主循环
print("开始并行计算...")
results = Parallel(n_jobs=-1)(delayed(process_point)(i, j, lat, lon)
                             for i, lat in enumerate(tqdm(lats, desc="纬度进度"))
                             for j, lon in enumerate(lons))

# 将结果填充到数组中
for i, j, tciw_aod, aod_tciw, tciw_aod_sig_flag, aod_tciw_sig_flag in results:
    tciw_to_aod[i, j] = tciw_aod
    aod_to_tciw[i, j] = aod_tciw
    tciw_to_aod_sig[i, j] = tciw_aod_sig_flag
    aod_to_tciw_sig[i, j] = aod_tciw_sig_flag

# 保存结果函数
def save_to_nc(data, sig_data, var_name, description):
    ds = xr.Dataset(
        {
            var_name: (["lat", "lon"], data),
            f"{var_name}_sig": (["lat", "lon"], sig_data)
        },
        coords={
            "lat": lats,
            "lon": lons
        }
    )
    ds.to_netcdf(f'{data_dir}/{var_name}_causal_effect_with_sig.nc')

# 保存结果
save_to_nc(tciw_to_aod, tciw_to_aod_sig, 'tciw_to_aod', 'Causal effect of TCIW on AOD550')
save_to_nc(aod_to_tciw, aod_to_tciw_sig, 'aod_to_tciw', 'Causal effect of AOD550 on TCIW')

# 绘制空间分布图
def plot_spatial(data, sig_data, title, save_name):
    plt.figure(figsize=(12, 8))
    ax = plt.axes(projection=ccrs.PlateCarree())
    ax.coastlines()
    
    im = ax.pcolormesh(lons, lats, data, cmap='RdBu_r', 
                      transform=ccrs.PlateCarree(), vmin=-1, vmax=1)
    plt.colorbar(im, ax=ax, orientation='horizontal', pad=0.05)
    
    sig_lons, sig_lats = np.where(sig_data)
    ax.scatter(lons[sig_lons], lats[sig_lats], color='black', s=5, 
              transform=ccrs.PlateCarree(), label='Significant (p < 0.05)')
    
    ax.set_title(title)
    ax.legend()
    plt.savefig(f'{data_dir}/{save_name}.png', dpi=300, bbox_inches='tight')
    plt.show()

# 绘制两张图
print("开始绘图...")
plot_spatial(tciw_to_aod, tciw_to_aod_sig, 
            'Causal Effect of TCIW on AOD550', 'tciw_to_aod')
plot_spatial(aod_to_tciw, aod_to_tciw_sig, 
            'Causal Effect of AOD550 on TCIW', 'aod_to_tciw')
print("处理完成！")