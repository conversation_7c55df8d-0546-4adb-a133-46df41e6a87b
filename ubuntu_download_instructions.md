# MERRA-2 AOD数据下载指南 (Ubuntu版本)

## 前提条件

1. **Ubuntu系统** (或WSL Ubuntu)
2. **网络连接** 
3. **足够的磁盘空间** (约25GB)

## 方法1: 使用Bash脚本 (推荐)

### 步骤1: 准备脚本
```bash
# 将脚本复制到Ubuntu系统中
# 如果使用WSL，脚本已经在 /mnt/d/dataset/program/ 目录下
cd /mnt/d/dataset/program/
```

### 步骤2: 设置权限并运行
```bash
# 给脚本执行权限
chmod +x ubuntu_download_merra2.sh

# 运行下载脚本
bash ubuntu_download_merra2.sh
```

## 方法2: 使用Python脚本

### 步骤1: 运行Python脚本
```bash
# 确保在正确目录
cd /mnt/d/dataset/program/

# 运行Python下载脚本
python3 ubuntu_download_merra2.py
```

## 方法3: 手动命令行操作

### 步骤1: 安装依赖
```bash
# 更新包管理器
sudo apt update

# 安装wget
sudo apt install -y wget

# 安装Python依赖
pip3 install xarray netcdf4 numpy --user
```

### 步骤2: 设置认证
```bash
# 创建.netrc文件
cat > ~/.netrc << EOF
machine urs.earthdata.nasa.gov
login willzhangyu1991
password Zy.768010991
EOF

# 设置权限
chmod 600 ~/.netrc
```

### 步骤3: 创建下载目录
```bash
mkdir -p /mnt/d/dataset/supercooled/merra2
cd /mnt/d/dataset/supercooled/merra2
```

### 步骤4: 手动下载示例
```bash
# 下载单个文件的示例命令
wget --load-cookies ~/.urs_cookies \
     --save-cookies ~/.urs_cookies \
     --keep-session-cookies \
     --no-check-certificate \
     --auth-no-challenge=on \
     --content-disposition \
     --timeout=300 \
     -O "filename.nc4" \
     "URL_HERE"
```

## 文件路径说明

- **URL文件位置**: `/mnt/d/willz/download/subset_M2IMNXGAS_5.12.4_20250809_073842_.txt`
- **下载目录**: `/mnt/d/dataset/supercooled/merra2/`
- **最终合并文件**: `/mnt/d/dataset/supercooled/80-24aodmerra2.nc`

## 预期结果

- **文件数量**: 541个NetCDF文件
- **总大小**: 约10-25GB
- **下载时间**: 2-6小时 (取决于网络速度)
- **最终文件**: 包含1980-2024年MERRA-2 AOD数据的合并文件

## 故障排除

### 1. 权限问题
```bash
# 如果遇到权限问题
sudo chown -R $USER:$USER /mnt/d/dataset/supercooled/
```

### 2. 网络问题
```bash
# 检查网络连接
ping urs.earthdata.nasa.gov

# 检查DNS
nslookup urs.earthdata.nasa.gov
```

### 3. 磁盘空间
```bash
# 检查可用空间
df -h /mnt/d/
```

### 4. 认证问题
```bash
# 检查.netrc文件
cat ~/.netrc
ls -la ~/.netrc
```

### 5. 重新开始下载
如果下载中断，重新运行脚本会自动跳过已下载的文件。

### 6. 查看失败的下载
```bash
# 查看失败的下载列表
cat /mnt/d/dataset/supercooled/merra2/failed_downloads.txt
```

## 监控进度

### 查看下载进度
```bash
# 查看已下载的文件数量
ls -1 /mnt/d/dataset/supercooled/merra2/*.nc4 | wc -l

# 查看下载目录大小
du -sh /mnt/d/dataset/supercooled/merra2/
```

### 实时监控
```bash
# 在另一个终端中监控
watch -n 30 'ls -1 /mnt/d/dataset/supercooled/merra2/*.nc4 | wc -l'
```

## 完成后验证

### 检查最终文件
```bash
# 检查合并后的文件
ls -lh /mnt/d/dataset/supercooled/80-24aodmerra2.nc

# 使用Python检查文件内容
python3 -c "
import xarray as xr
ds = xr.open_dataset('/mnt/d/dataset/supercooled/80-24aodmerra2.nc')
print('变量:', list(ds.variables.keys()))
print('维度:', dict(ds.dims))
print('时间范围:', ds.time.min().values, '到', ds.time.max().values)
ds.close()
"
```

## 注意事项

1. **保持网络连接稳定**
2. **确保有足够的磁盘空间**
3. **不要同时运行多个下载脚本**
4. **如果下载中断，可以重新运行脚本继续**
5. **下载完成后可以删除单个.nc4文件以节省空间**

## 联系支持

如果遇到问题，请检查：
1. 网络连接
2. NASA Earthdata账户状态
3. 磁盘空间
4. 文件权限

祝下载顺利！
