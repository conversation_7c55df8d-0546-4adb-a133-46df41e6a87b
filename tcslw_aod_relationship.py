import xarray as xr
import numpy as np
import matplotlib.pyplot as plt
from statsmodels.nonparametric.smoothers_lowess import lowess
from scipy.stats import gaussian_kde

# 读取数据
aod_mean = xr.open_dataset('D:/dataset/supercooled/aodmean.nc')
aod_tcslw = xr.open_dataset('D:/dataset/supercooled/aod550_to_tcslw_multi_tau.nc')

# 创建目标网格 (使用正确的维度名称)
target_grid = xr.Dataset({
    'latitude': np.linspace(-90, 90, 180),
    'longitude': np.linspace(0, 359, 360)
})

# 使用xarray进行插值(使用正确的维度名称)
aod_mean = aod_mean.interp(
    latitude=target_grid.latitude,
    longitude=target_grid.longitude,
    method='nearest'
)

# 确保使用相同的网格点
aod_mean = aod_mean.interp_like(aod_tcslw)  # 将aod_mean插值到aod_tcslw的网格上

# 处理数据并筛选
x = aod_mean['aod550'].values.flatten()
y = aod_tcslw['aod550_to_tcslw_tau1'].values.flatten()
mask = (~np.isnan(x)) & (~np.isnan(y)) & (x > 0) & (y != 0)
x = x[mask]
y = y[mask]

# 创建图形和双坐标轴
fig, ax1 = plt.subplots(figsize=(12, 8))

# 计算概率密度
xy = np.vstack([x, y])
kde = gaussian_kde(xy)
z = kde(xy)

# 绘制散点图（颜色表示概率密度）
sc = ax1.scatter(x, y, c=z, s=10, alpha=0.5, cmap='viridis', label='Data points')

# LOESS拟合
loess_smoothed = lowess(y, x, frac=0.3, it=3)
ax1.plot(loess_smoothed[:, 0], loess_smoothed[:, 1], 'r-', linewidth=3, label='LOESS fit')

# 设置x轴范围从0到0.25
ax1.set_xlim(0, 0.25)  # 修改为0-0.25范围
# 修改图形标签
ax1.set_xlabel('AOD550')  # 更新x轴标签
ax1.set_ylabel('Causal Effect of AOD550 on TCSLW')  # 更新y轴标签
ax1.set_title('The LOESS Relationship between AOD550 and the Causal Effect of AOD550 on TCSLW')  # 更新标题

# 添加颜色条
cbar = plt.colorbar(sc, ax=ax1, label='Probability Density')

# 图形元素
ax1.set_title('The LOESS Relationship between TCSLW and the Causal Effect of AOD on TCSLW')
ax1.legend(loc='upper left')
ax1.grid(True)

# 保存图形
plt.savefig('D:/dataset/supercooled/tcslw_aod_loess_0.01-0.12.png', dpi=600, bbox_inches='tight')
plt.show()