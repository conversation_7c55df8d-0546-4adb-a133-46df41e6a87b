import os
import subprocess
import xarray as xr
from pathlib import Path
import time

def setup_netrc():
    """设置.netrc文件用于wget认证"""
    print("NASA Earthdata认证设置")
    print("=" * 30)
    print("请确保您已经在NASA Earthdata注册账户")
    print("网址: https://urs.earthdata.nasa.gov/")
    
    username = input("请输入您的NASA Earthdata用户名: ").strip()
    password = input("请输入您的NASA Earthdata密码: ").strip()
    
    # 创建.netrc文件
    netrc_path = Path.home() / '.netrc'
    
    # 检查是否已存在.netrc文件
    if netrc_path.exists():
        print(f".netrc文件已存在: {netrc_path}")
        overwrite = input("是否覆盖现有的.netrc文件? (y/n): ").strip().lower()
        if overwrite != 'y':
            print("使用现有的.netrc文件")
            return True
    
    try:
        with open(netrc_path, 'w') as f:
            f.write(f"machine urs.earthdata.nasa.gov\n")
            f.write(f"login {username}\n")
            f.write(f"password {password}\n")
        
        # 设置文件权限 (仅所有者可读写)
        os.chmod(netrc_path, 0o600)
        print(f"✓ .netrc文件已创建: {netrc_path}")
        return True
        
    except Exception as e:
        print(f"创建.netrc文件失败: {e}")
        return False

def download_with_wget(url, file_path, max_retries=3):
    """使用wget下载文件"""
    for attempt in range(max_retries):
        try:
            cmd = [
                'wget',
                '--load-cookies', '~/.urs_cookies',
                '--save-cookies', '~/.urs_cookies',
                '--auth-no-challenge',
                '--keep-session-cookies',
                '--content-disposition',
                '-O', str(file_path),
                url
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)
            
            if result.returncode == 0:
                return True
            else:
                print(f"wget错误 (尝试 {attempt + 1}): {result.stderr}")
                if attempt < max_retries - 1:
                    time.sleep(5)
                
        except subprocess.TimeoutExpired:
            print(f"下载超时 (尝试 {attempt + 1})")
            if attempt < max_retries - 1:
                time.sleep(5)
        except Exception as e:
            print(f"下载错误 (尝试 {attempt + 1}): {e}")
            if attempt < max_retries - 1:
                time.sleep(5)
    
    return False

def check_wget():
    """检查wget是否可用"""
    try:
        result = subprocess.run(['wget', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✓ wget可用")
            return True
        else:
            print("✗ wget不可用")
            return False
    except FileNotFoundError:
        print("✗ 未找到wget命令")
        print("请安装wget或使用其他下载方法")
        return False

def main():
    """主函数"""
    print("MERRA-2 AOD数据下载工具 (使用wget)")
    print("=" * 40)
    
    # 检查wget
    if not check_wget():
        print("\n替代方案:")
        print("1. 安装wget: https://www.gnu.org/software/wget/")
        print("2. 使用download_merra2_auth.py (Python requests版本)")
        print("3. 手动下载文件")
        return
    
    # 配置路径
    url_file_path = input("请输入URL文件的完整路径 (直接回车使用默认路径): ").strip()
    if not url_file_path:
        url_file_path = r'D:\willz\download\subset_M2IMNXGAS_5.12.4_20250809_073842_.txt'
    
    download_dir = Path(r'D:\dataset\supercooled\merra2')
    output_file = r'D:\dataset\supercooled\80-24aodmerra2.nc'
    
    print(f"\nURL文件路径: {url_file_path}")
    print(f"下载目录: {download_dir}")
    print(f"输出文件: {output_file}")
    
    # 创建下载目录
    download_dir.mkdir(parents=True, exist_ok=True)
    
    # 检查URL文件
    if not os.path.exists(url_file_path):
        print(f"错误: 找不到文件 {url_file_path}")
        return
    
    # 设置认证
    if not setup_netrc():
        return
    
    # 读取URL
    print("\n正在读取下载地址...")
    try:
        with open(url_file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        urls = []
        for line in lines:
            line = line.strip()
            if line and (line.startswith('http://') or line.startswith('https://')):
                urls.append(line)
        
        print(f"找到 {len(urls)} 个下载地址")
        
        if len(urls) == 0:
            print("没有找到有效的下载地址")
            return
            
    except Exception as e:
        print(f"读取文件出错: {e}")
        return
    
    # 下载文件
    downloaded_files = []
    failed_downloads = []
    
    print(f"\n开始下载 {len(urls)} 个文件...")
    print("注意: 下载可能需要较长时间，请耐心等待...")
    
    for i, url in enumerate(urls, 1):
        try:
            # 从URL提取文件名
            filename = os.path.basename(url.split('?')[0])
            if not filename.endswith('.nc4'):
                filename = f"merra2_aod_{i:04d}.nc4"
            
            file_path = download_dir / filename
            
            # 检查文件是否已存在
            if file_path.exists():
                print(f"[{i}/{len(urls)}] {filename} 已存在，跳过")
                downloaded_files.append(str(file_path))
                continue
            
            print(f"[{i}/{len(urls)}] 正在下载 {filename}...")
            
            # 使用wget下载
            success = download_with_wget(url, file_path)
            
            if success and file_path.exists():
                downloaded_files.append(str(file_path))
                print(f"[{i}/{len(urls)}] ✓ {filename} 下载完成")
            else:
                failed_downloads.append(url)
                print(f"[{i}/{len(urls)}] ✗ {filename} 下载失败")
                # 删除可能的空文件
                if file_path.exists():
                    file_path.unlink()
            
            # 添加延迟
            time.sleep(1)
            
        except Exception as e:
            failed_downloads.append(url)
            print(f"[{i}/{len(urls)}] ✗ 下载失败: {e}")
    
    print(f"\n下载完成:")
    print(f"✓ 成功: {len(downloaded_files)} 个文件")
    print(f"✗ 失败: {len(failed_downloads)} 个文件")
    
    if len(downloaded_files) == 0:
        print("没有成功下载任何文件")
        return
    
    # 合并文件
    print(f"\n开始合并 {len(downloaded_files)} 个文件...")
    try:
        datasets = []
        valid_files = []
        
        for file_path in downloaded_files:
            try:
                ds = xr.open_dataset(file_path)
                datasets.append(ds)
                valid_files.append(file_path)
                print(f"✓ 加载 {os.path.basename(file_path)}")
            except Exception as e:
                print(f"✗ 加载失败 {os.path.basename(file_path)}: {e}")
        
        if len(datasets) == 0:
            print("没有有效的数据集可以合并")
            return
        
        print("正在合并数据集...")
        merged_ds = xr.concat(datasets, dim='time')
        merged_ds = merged_ds.sortby('time')
        
        print(f"正在保存到 {output_file}...")
        merged_ds.to_netcdf(output_file)
        
        # 关闭数据集
        for ds in datasets:
            ds.close()
        merged_ds.close()
        
        print(f"\n✓ 处理完成！")
        print(f"✓ 成功下载: {len(downloaded_files)} 个文件")
        print(f"✓ 成功合并: {len(valid_files)} 个文件")
        print(f"✓ 输出文件: {output_file}")
        
        # 显示文件信息
        with xr.open_dataset(output_file) as ds:
            print(f"\n合并后的数据集信息:")
            print(f"变量: {list(ds.variables.keys())}")
            print(f"维度: {dict(ds.dims)}")
            if 'time' in ds.dims:
                print(f"时间范围: {ds.time.min().values} 到 {ds.time.max().values}")
        
        # 保存失败列表
        if failed_downloads:
            failed_file = download_dir / "failed_downloads.txt"
            with open(failed_file, 'w') as f:
                for url in failed_downloads:
                    f.write(url + '\n')
            print(f"\n失败的下载地址已保存到: {failed_file}")
        
    except Exception as e:
        print(f"合并过程出错: {e}")

if __name__ == "__main__":
    main()
