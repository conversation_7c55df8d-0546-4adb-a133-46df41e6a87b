import numpy as np
from scipy.interpolate import interp1d
import xarray as xr
from statsmodels.nonparametric.smoothers_lowess import lowess

# 从LOESS结果中找到y=0对应的x值
def find_zero_crossing():
    try:
        print("Loading datasets...")
        # 使用chunks参数来避免内存问题
        aod_mean = xr.open_dataset('D:/dataset/supercooled/aodmean.nc', chunks={'time': 100})
        print("aod_mean loaded")
        aod_tcslw = xr.open_dataset('D:/dataset/supercooled/aod550_to_tcslw_multi_tau.nc', chunks={'time': 100})
        print("aod_tcslw loaded")
    except Exception as e:
        print(f"Error loading datasets: {e}")
        return None
    
    # 数据处理
    print("Interpolating data...")
    aod_mean = aod_mean.interp_like(aod_tcslw)
    print("Interpolation complete")

    print("Extracting arrays...")
    x = aod_mean['aod550'].values.flatten()
    y = aod_tcslw['aod550_to_tcslw_tau1'].values.flatten()

    # 调试信息
    print(f"x shape: {x.shape}, y shape: {y.shape}")

    # 确保两个数组有相同的长度
    min_len = min(len(x), len(y))
    x = x[:min_len]
    y = y[:min_len]

    print("Creating mask...")
    mask = (~np.isnan(x)) & (~np.isnan(y)) & (x > 0) & (y != 0)
    x = x[mask]
    y = y[mask]
    print(f"After masking: x shape: {x.shape}, y shape: {y.shape}")
    
    # LOESS拟合
    print("Performing LOESS fitting...")
    loess_smoothed = lowess(y, x, frac=0.3, it=3)
    print("LOESS fitting complete")
    
    # 找到y=0的交点
    interp_func = interp1d(loess_smoothed[:, 1], loess_smoothed[:, 0], 
                          kind='linear', fill_value='extrapolate')
    zero_crossing = interp_func(0)
    
    print(f"AOD值在因果效应为0时: {zero_crossing:.4f}")
    return zero_crossing

if __name__ == "__main__":
    find_zero_crossing()