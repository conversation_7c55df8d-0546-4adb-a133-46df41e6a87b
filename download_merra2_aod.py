import os
import requests
import xarray as xr
import numpy as np
from pathlib import Path
import time
from urllib.parse import urlparse
import glob

def create_directories():
    """创建必要的目录"""
    download_dir = Path(r'D:\dataset\supercooled\merra2')
    download_dir.mkdir(parents=True, exist_ok=True)
    return download_dir

def read_download_urls(url_file_path):
    """读取下载地址文件"""
    try:
        with open(url_file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # 过滤出有效的URL
        urls = []
        for line in lines:
            line = line.strip()
            if line and (line.startswith('http://') or line.startswith('https://')):
                urls.append(line)
        
        print(f"Found {len(urls)} download URLs")
        return urls
    
    except FileNotFoundError:
        print(f"Error: File {url_file_path} not found")
        return []
    except Exception as e:
        print(f"Error reading file: {e}")
        return []

def download_file(url, download_dir, session=None):
    """下载单个文件"""
    try:
        # 从URL中提取文件名
        parsed_url = urlparse(url)
        filename = os.path.basename(parsed_url.path)
        
        if not filename or not filename.endswith('.nc4'):
            # 如果无法从URL提取文件名，使用时间戳
            filename = f"merra2_aod_{int(time.time())}.nc4"
        
        file_path = download_dir / filename
        
        # 检查文件是否已存在
        if file_path.exists():
            print(f"File {filename} already exists, skipping...")
            return str(file_path)
        
        print(f"Downloading {filename}...")
        
        # 使用session或requests下载
        if session:
            response = session.get(url, stream=True)
        else:
            response = requests.get(url, stream=True)
        
        response.raise_for_status()
        
        # 写入文件
        with open(file_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
        
        print(f"Successfully downloaded {filename}")
        return str(file_path)
    
    except Exception as e:
        print(f"Error downloading {url}: {e}")
        return None

def download_all_files(urls, download_dir):
    """下载所有文件"""
    downloaded_files = []
    
    # 创建session以重用连接
    with requests.Session() as session:
        for i, url in enumerate(urls, 1):
            print(f"\nDownloading file {i}/{len(urls)}")
            file_path = download_file(url, download_dir, session)
            
            if file_path:
                downloaded_files.append(file_path)
            
            # 添加小延迟避免过于频繁的请求
            time.sleep(1)
    
    print(f"\nDownload completed. {len(downloaded_files)} files downloaded successfully.")
    return downloaded_files

def merge_nc_files(file_list, output_path):
    """合并NetCDF文件"""
    try:
        print(f"\nMerging {len(file_list)} NetCDF files...")
        
        # 读取所有文件
        datasets = []
        for file_path in file_list:
            try:
                ds = xr.open_dataset(file_path)
                datasets.append(ds)
                print(f"Loaded {os.path.basename(file_path)}")
            except Exception as e:
                print(f"Error loading {file_path}: {e}")
        
        if not datasets:
            print("No valid datasets to merge")
            return False
        
        # 合并数据集
        print("Concatenating datasets...")
        merged_ds = xr.concat(datasets, dim='time')
        
        # 排序时间维度
        merged_ds = merged_ds.sortby('time')
        
        # 保存合并后的文件
        print(f"Saving merged dataset to {output_path}...")
        merged_ds.to_netcdf(output_path)
        
        # 关闭数据集
        for ds in datasets:
            ds.close()
        merged_ds.close()
        
        print(f"Successfully merged files into {output_path}")
        return True
    
    except Exception as e:
        print(f"Error merging files: {e}")
        return False

def main():
    """主函数"""
    # 配置路径 - 请根据实际情况修改
    url_file_path = r'D:\willz\download\subset_M2IMNXGAS_5.12.4_20250809_073842_.txt'
    output_file = r'D:\dataset\supercooled\80-24aodmerra2.nc'
    
    print("MERRA-2 AOD Data Download and Merge Tool")
    print("=" * 50)
    
    # 创建下载目录
    download_dir = create_directories()
    print(f"Download directory: {download_dir}")
    
    # 读取下载地址
    print(f"\nReading URLs from: {url_file_path}")
    urls = read_download_urls(url_file_path)
    
    if not urls:
        print("No valid URLs found. Please check the input file.")
        return
    
    # 下载文件
    downloaded_files = download_all_files(urls, download_dir)
    
    if not downloaded_files:
        print("No files were downloaded successfully.")
        return
    
    # 合并文件
    success = merge_nc_files(downloaded_files, output_file)
    
    if success:
        print(f"\n✓ Process completed successfully!")
        print(f"✓ Downloaded {len(downloaded_files)} files")
        print(f"✓ Merged data saved to: {output_file}")
        
        # 显示合并后文件的信息
        try:
            with xr.open_dataset(output_file) as ds:
                print(f"\nMerged dataset info:")
                print(f"Variables: {list(ds.variables.keys())}")
                print(f"Dimensions: {dict(ds.dims)}")
                if 'time' in ds.dims:
                    print(f"Time range: {ds.time.min().values} to {ds.time.max().values}")
        except Exception as e:
            print(f"Error reading merged file info: {e}")
    else:
        print("✗ Merge process failed.")

if __name__ == "__main__":
    main()
